using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Schema;
using System.Xml.Serialization;
using ca.nrcan.gc.OEE.HouseFileLibrary.CodesComponent;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.BaseLoadsComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.FoundationComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.GenerationComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.HeatingCoolingComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.HouseComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.NaturalAirInfiltrationComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.ResultsComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.Components.VentilationComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.FuelCostsComponents;
using ca.nrcan.gc.OEE.HouseFileLibrary.HouseFileEvents;
using ca.nrcan.gc.OEE.HouseFileLibrary.H2kResources;
#if !NO_SERIALIZERS
using Microsoft.Xml.Serialization.GeneratedAssembly;
#endif

namespace ca.nrcan.gc.OEE.HouseFileLibrary
{
    [Serializable, XmlRoot("HouseFile")]
    public class HouseFile
    {
        #region Public Serialized Properties
        [XmlAttribute("xml:lang")]
        public string XmlLang
        {
            get { return Language; }
            set { Language = LanguageOptions.FromLanguageCode(value); }
        }
        
        [XmlIgnore]
        LanguageOptions Language;

        [XmlAttribute("uiUnits")]
        public string UiUnits;

        [XmlElement("Version")]
        public HouseFileVersion Version;

        [XmlElement("Application")]
        public Application Application = new Application();

        [XmlElement("ProgramInformation")]
        public ProgramInformation ProgramInformation = new ProgramInformation();

        [XmlElement("House")]
        public House House = new House();

        [XmlElement("Codes")]
        public Codes Codes;

        [XmlElement("EnergyUpgrades")]
        public EnergyUpgradesComponents.EnergyUpgrades EnergyUpgrades;

        [XmlElement("FuelCosts")]
        public FuelCosts FuelCosts = new FuelCosts();

        [XmlArray("AllResults"), XmlArrayItem(typeof(Results))]
        public List<Results> AllResults;
        #endregion

        #region Public Non-Serialized Properties
        [XmlIgnore]
        public static List<HouseFileError> Errors = new List<HouseFileError>();

        [XmlIgnore]
        public List<BaseComponent> HouseComponents
        {
            get
            {
                List<BaseComponent> allComponents = new List<BaseComponent>();
                AddAllComponentsToList(House, allComponents);

                // Special case components that are children of House
                allComponents.Add(House.BaseLoads);
                allComponents.Add(House.Generation);
                allComponents.Add(House.HeatingCooling);
                allComponents.Add(House.NaturalAirInfiltration);
                allComponents.Add(House.Temperatures);
                if (House.Ventilation != null)
                    allComponents.Add(House.Ventilation);

                return allComponents;
            }
        }

        static void AddAllComponentsToList(BaseComponent root, List<BaseComponent> list)
        {
            if (root != null && list != null)
            {
                // Base case: add this component to the list
                list.Add(root);

                // Recursive case: add it's children and their descendants
                if (root.Components != null)
                {
                    foreach (var child in root.Components)
                        AddAllComponentsToList(child, list);
                }
            }
        }



        public uint NextId
        {
            get
            {
                // Remember: ID 0 is reserved for the House object
                var allComponents = HouseComponents;
                if (allComponents.Count == 0) return 1;
                uint topId = allComponents.Select(c => c.Id).Max();
                return ++topId;
            }
        }

        [XmlIgnore]
        public XElement Program = null;

        [XmlIgnore]
        public const string LibraryName = "HouseFile Library";

        [XmlIgnore]
        public static HouseFileVersion LibraryVersion
        { get { return new HouseFileVersion( typeof(HouseFile).Assembly.GetName().Version ); } }
        #endregion

        #region Public methods
        public Component CreateUpgradeFromComponent(BaseComponent unupgraded)
        {
            if (unupgraded == null || !CanBeUpgraded(unupgraded, (Program != null))) return null;

            Component upgrade = Clone( (Component)unupgraded );
            upgrade.Components = null;
            SaveUpgrade(upgrade);
            return upgrade;
        }

        public Component CreateUpgradeFromComponent(uint id)
        {
            return CreateUpgradeFromComponent( GetComponentById(id) );
        }

        public BaseComponent GetComponentById(uint id)
        {
            return HouseComponents.Where(component => component.Id == id).FirstOrDefault();
        }

        public List<T> GetComponentsByType<T>()
        {
            return HouseComponents.Where(component => component is T).Cast<T>().ToList();
        }

        /// <summary>
        /// Returns a copy of this HouseFile with the base components swapped out for the upgraded ones.
        /// </summary>
        /// <returns>A copy of this HouseFile with the base components swapped out for the upgraded ones.</returns>
        public HouseFile GetUpgradedHouse()
        {
            // Start with a copy of this house
            HouseFile upgradedHouse = new HouseFile();
            upgradedHouse.Load(this);

            // Sanity check: do we even have upgrades to apply?
            if ( upgradedHouse.HasUpgrades() )
            {
                // For each upgrade...
                foreach (var upgrade in upgradedHouse.EnergyUpgrades.Components)
                {
                    // Find the corresponding base component
                    var toUpgrade = upgradedHouse.GetComponentById(upgrade.Id);
                    if (toUpgrade != null)
                    {
                        // Move the base component's children to the upgrade component
                        upgrade.Components = toUpgrade.Components;

                        // Special cases
                        if (upgrade is BaseLoads)
                            upgradedHouse.House.BaseLoads = (BaseLoads)upgrade;

                        else if (upgrade is Generation)
                            upgradedHouse.House.Generation = (Generation)upgrade;

                        else if (upgrade is HeatingCooling)
                            upgradedHouse.House.HeatingCooling = (HeatingCooling)upgrade;

                        else if (upgrade is NaturalAirInfiltration)
                            upgradedHouse.House.NaturalAirInfiltration = (NaturalAirInfiltration)upgrade;

                        else if (upgrade is Temperatures)
                            upgradedHouse.House.Temperatures = (Temperatures)upgrade;

                        else if (upgrade is Ventilation)
                            upgradedHouse.House.Ventilation = (Ventilation)upgrade;
                        
                        // Base case
                        else
                        {
                            // Update the parent's component list to use the upgrade component
                            var parent = toUpgrade.Parent;
                            if (parent != null)
                            {
                                parent.Components.Remove(toUpgrade);
                                parent.Components.Add(upgrade);
                            }
                        }
                    }
                }

                // We've applied all existing upgrades so clear them out
                upgradedHouse.EnergyUpgrades = null;

                // The results apply to the not upgraded house so clear them out
                upgradedHouse.AllResults = null;

                // Rebuild the HouseComponents
                upgradedHouse.UpdateReferences();
            }

            // Return house with upgrades applied
            return upgradedHouse;
        }

        public Component GetUpgradeById(uint id)
        {
            return EnergyUpgrades.Components.Where(component => component.Id == id).FirstOrDefault(); ;
        }

        public List<T> GetUpgradesByType<T>()
        {
            return EnergyUpgrades == null? new List<T>() : EnergyUpgrades.Components.Where(component => component is T).Cast<T>().ToList();
        }

        public Component GetUpgradeForComponent(BaseComponent unupgraded)
        {
            if (unupgraded == null) return null;
            return GetUpgradeById(unupgraded.Id);
        }

        public static bool HasErrors()
        {
            return Errors.Count > 0;
        }

        public bool HasUpgrades()
        {
            if (EnergyUpgrades == null)
                return false;

            return (EnergyUpgrades.Components.Count > 0);
        }

        public bool IsSame(HouseFile otherHouse)
        {
            // TODO: this is wrong, implement proper semantics
            return (this == otherHouse);
        }

        public void MoveComponentToNewParent(BaseComponent toMove, BaseComponent newParent)
        {
            // TODO: ensure new parent allows components of this type as children
            if (toMove.Parent != null)
                toMove.Parent.Components.Remove( toMove );

            toMove.Parent = newParent;
            
            if (newParent != null)
                newParent.Components.Add( toMove );
        }

        public void RemoveComponent(BaseComponent toRemove)
        {
            // Sanity check: do we have anything to do?
            if (toRemove == null) return;

            // When removing a base component the equivalent upgrade must also be removed
            if (EnergyUpgrades != null)
            {
                Component matchingUpgrade = EnergyUpgrades.Components.Where(c => c.Id == toRemove.Id).FirstOrDefault();
                if (matchingUpgrade != null) RemoveUpgrade(matchingUpgrade);
            }

            // Recursively remove all children
            if (toRemove.Components != null)
                while (toRemove.Components.Count > 0)
                {
                    RemoveComponent( toRemove.Components[0] );
                }

            // TODO: prevent removal of required components
            // TODO: special handling of special case children of House
            if (toRemove.Parent != null)
                toRemove.Parent.Components.Remove(toRemove);
        }

        public void RemoveComponent(uint id)
        {
            RemoveComponent( GetComponentById(id) );
        }

        public void RemoveUpgrade(Component upgradeToRemove)
        {
            // Sanity check
            if (upgradeToRemove == null) return;

            // Recursively remove all children
            if (upgradeToRemove.Components != null)
                while (upgradeToRemove.Components.Count > 0)
                {
                    RemoveComponent(upgradeToRemove.Components[0]);
                }

            EnergyUpgrades.Components.Remove(upgradeToRemove);
        }

        public void RemoveUpgrade(uint id)
        {
            RemoveUpgrade( GetUpgradeById(id) );
        }

        public void SaveComponent(BaseComponent toSave, BaseComponent parent = null)
        {
            if (toSave.Id == 0 && !(toSave is House))
                toSave.Id = NextId;
            else
            {
                BaseComponent existingComponent = GetComponentById(toSave.Id);
                RemoveComponent(existingComponent);
            }

            if (parent != null)
            {
                toSave.Parent = parent;

                if (toSave.Parent.Components == null)
                    toSave.Parent.Components = new List<BaseComponent>();
                
                toSave.Parent.Components.Add(toSave);
            }
        }

        public void SaveUpgrade(Component toSave)
        {
            // Sanity check: cannot upgrade house!
            if (toSave.Id == 0) return;

            // We want to replace any existing upgrade there might be
            RemoveUpgrade( GetUpgradeById(toSave.Id) );

            EnergyUpgrades.Components.Add(toSave);
        }

        public static bool CanBeUpgraded(BaseComponent toCheck, bool hasProgram = false)
        {
            // Whitelist of components that can be upgraded
            // Note: BaseLoads and Temperatures only when there's no program
            if ((toCheck is BaseLoads && !hasProgram)
                || toCheck is Components.CeilingComponents.Ceiling
                || toCheck is Components.DoorComponents.Door
                || toCheck is Components.FloorComponents.Floor
                || toCheck is Components.FloorHeaderComponents.FloorHeader
                || toCheck is Generation
                || toCheck is HeatingCooling
                || toCheck is Components.HotWaterComponents.HotWater
                || toCheck is Foundation
                || toCheck is NaturalAirInfiltration
                || toCheck is Components.RoomComponents.Room
                || (toCheck is Temperatures && !hasProgram)
                || toCheck is Ventilation
                || toCheck is Components.WallComponents.Wall
                || toCheck is Components.WindowComponents.Window)
                return true;

            // Anything else cannot
            return false;
        }

        public static bool ValidateAgainstSchema(string xmlUri, string xsdUri)
        { return ValidateAgainstSchema(XDocument.Load(xmlUri), xsdUri); }

        public static bool ValidateAgainstSchema(XDocument xmlFile, string xsdUri)
        {
            XmlSchemaSet schemas = new XmlSchemaSet();
            schemas.Add("", xsdUri);

            bool valid = true;
            xmlFile.Validate(schemas, (o, e) =>
            {
                Errors.Add(new HouseFileError(e.Message, e.Message));
                valid = false;
            });

            return valid;
        }
        #endregion

        #region Public methods for Nominal Insulation
        public class NominalInsulationPercentage
        {
            public decimal NominalInsulation;
            public decimal TotalAreaPercentage;

            public NominalInsulationPercentage(decimal nominalInsulation, decimal totalAreaPercentage)
            {
                this.NominalInsulation = nominalInsulation;
                this.TotalAreaPercentage = totalAreaPercentage;
            }
        }

        static void AdjustPercentages(uint decimalPlaceForPercentages, List<NominalInsulationPercentage> groups, List<NominalInsulationPercentage> secondGroups = null)
        {
            // Sanity check: do we even have data?
            if (groups == null || groups.Count == 0) return;

            // These will be used to keep track of the entry with the greatest rounding error in each direction
            decimal minError = 0, maxError = 0;
            NominalInsulationPercentage minKeyValue = groups[0], maxKeyValue = groups[0];

            // These will be used through each loop iteration for calculations
            decimal roundingError = 0, roundedValue = 0, runningTotal = 0;

            // For each percentage...
            foreach (var keyValue in groups)
            {
                // Round the percentage and calculate the rounding error
                roundedValue = decimal.Round(keyValue.TotalAreaPercentage, (int)decimalPlaceForPercentages, MidpointRounding.AwayFromZero);
                roundingError = roundedValue - keyValue.TotalAreaPercentage;

                if (roundingError < minError)
                {
                    // Keep track of the new rounding error as min and its key in groups1
                    minError = roundingError;
                    minKeyValue = keyValue;
                }
                else if (roundingError > maxError)
                {
                    // Keep track of the new rounding error as max and its key in groups1
                    maxError = roundingError;
                    maxKeyValue = keyValue;
                }

                // Keep a running total of the rounded percentages
                keyValue.TotalAreaPercentage = roundedValue;
                runningTotal += roundedValue;
            }

            // if we have a group2...
            if (secondGroups != null && secondGroups.Count > 0)
            {
                // Then do the same for each percentage in groups2
                foreach (var keyValue in secondGroups)
                {
                    // Round the percentage and calculate the rounding error
                    roundedValue = decimal.Round(keyValue.TotalAreaPercentage, (int)decimalPlaceForPercentages, MidpointRounding.AwayFromZero);
                    roundingError = roundedValue - keyValue.TotalAreaPercentage;

                    if (roundingError < minError)
                    {
                        // Keep track of the new rounding error as min and its key in groupsCeilingNonFlat
                        minError = roundingError;
                        minKeyValue = keyValue;
                    }
                    else if (roundingError > maxError)
                    {
                        // Keep track of the new rounding error as max and its key in groupsCeilingNonFlat
                        maxError = roundingError;
                        maxKeyValue = keyValue;
                    }

                    // Keep a running total of the rounded percentages
                    keyValue.TotalAreaPercentage = roundedValue;
                    runningTotal += roundedValue;
                }
            }

            // Do we even need to make any adjustments?
            if (runningTotal != 100.0m)
            {
                // How much do we need to adjust to get exactly 100% ?
                decimal adjustment = 100m - runningTotal;

                // Adjust the percentage of the entry that had the greatest rounding error in the opposite direction of the adjustment to minimize overall rounding error
                if (adjustment < 0)
                    maxKeyValue.TotalAreaPercentage += adjustment;
                else
                    minKeyValue.TotalAreaPercentage += adjustment;
            }
        }

        static string BuildNominalInsulationString(List<NominalInsulationPercentage> groups)
        {
            StringBuilder nominalInsulation = new StringBuilder();
            bool isFirst = true;
            foreach (var keyValue in groups)
            {
                if (isFirst)
                    isFirst = false;
                else
                    nominalInsulation.Append(';');

                nominalInsulation.Append(keyValue.TotalAreaPercentage);
                nominalInsulation.Append(";");
                nominalInsulation.Append(keyValue.NominalInsulation.ToString(LanguageOptions.FromCurrentCulture().CultureInfo));
            }

            return nominalInsulation.ToString();
        }

        public string GetCeilingFlatNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            return GetCeilingNominalInsulations(true, decimalPlaceForPercentages, decimalPlaceForInsulation, enumerateAll);
        }

        public string GetCeilingNominalInsulations(bool isFlat = false, uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groupsFlat = new List<NominalInsulationPercentage>();
            List<NominalInsulationPercentage> groupsNonFlat = new List<NominalInsulationPercentage>();

            List<Components.CeilingComponents.Ceiling> allCeilings = GetComponentsByType<Components.CeilingComponents.Ceiling>();
            if (allCeilings != null && allCeilings.Count > 0)
            {
                allCeilings = allCeilings.OrderBy(c => c.Id).ToList();
                decimal totalArea = allCeilings.Sum(f => f.Measurements.Area);

                NominalInsulationPercentage existing = null;
                foreach (var ceiling in allCeilings)
                {
                    decimal rValueKey = decimal.Round( Conversions.RSItoR(ceiling.Construction.CeilingType.NominalInsulation),
                                                       (int)decimalPlaceForInsulation );
                    decimal percentage = 100m * ceiling.Measurements.Area / totalArea;

                    // if ceiling is Cathedral or Flat
                    if (ceiling.Construction.Type == CeilingTypes.Cathedral || ceiling.Construction.Type == CeilingTypes.Flat)
                    {
                        if (!enumerateAll)
                            existing = groupsFlat.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                        if (existing == null)
                            groupsFlat.Add( new NominalInsulationPercentage(rValueKey, percentage) );
                        else
                            existing.TotalAreaPercentage += percentage;
                    }
                    else // ceiling is non-flat
                    {
                        if (!enumerateAll)
                            existing = groupsNonFlat.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                        if (existing == null)
                            groupsNonFlat.Add( new NominalInsulationPercentage(rValueKey, percentage) );
                        else
                            existing.TotalAreaPercentage += percentage;
                    }
                }

                if (groupsFlat.Count > 0)
                    AdjustPercentages(decimalPlaceForPercentages, groupsFlat, groupsNonFlat);
                else
                    AdjustPercentages(decimalPlaceForPercentages, groupsNonFlat);

                if (isFlat)
                    return BuildNominalInsulationString(groupsFlat);
                else
                    return BuildNominalInsulationString(groupsNonFlat);
            }

            return string.Empty;
        }

        public string GetCeilingNonFlatNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            return GetCeilingNominalInsulations(false, decimalPlaceForPercentages, decimalPlaceForInsulation, enumerateAll);
        }

        public string GetCeilingTypes()
        {
            var allCeilings = GetComponentsByType<Components.CeilingComponents.Ceiling>();
            if (allCeilings == null || allCeilings.Count == 0) return string.Empty;

            int flatCount = allCeilings.OrderBy(c => c.Id).Count(c => c.Construction.Type == CeilingTypes.Cathedral || c.Construction.Type == CeilingTypes.Flat);
            int nonFlatCount = allCeilings.Count - flatCount;

            StringBuilder ceilingTypes = new StringBuilder();
            for (var i = 0; i < nonFlatCount; ++i)
            {
                ceilingTypes.Append('A');
                if (i < nonFlatCount - 1)
                    ceilingTypes.Append(';');
            }

            if (nonFlatCount > 0 && flatCount > 0)
                ceilingTypes.Append(';');

            for (var i = 0; i < flatCount; ++i)
            {
                ceilingTypes.Append('F');
                if (i < flatCount - 1)
                    ceilingTypes.Append(';');
            }

            return ceilingTypes.ToString();
        }

        public string GetFloorNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            List<Components.FloorComponents.Floor> allMyFloors = GetComponentsByType<Components.FloorComponents.Floor>();
            if (allMyFloors != null && allMyFloors.Count > 0)
            {
                allMyFloors = allMyFloors.OrderBy(f => f.Id).ToList();
                decimal totalArea = allMyFloors.Sum(f => f.Measurements.Area);

                NominalInsulationPercentage existing = null;
                foreach (var floor in allMyFloors)
                {
                    decimal rValueKey = decimal.Round(Conversions.RSItoR(floor.Construction.Type.NominalInsulation),
                                                       (int)decimalPlaceForInsulation);
                    decimal percentage = 100m * floor.Measurements.Area / totalArea;

                    if (!enumerateAll)
                        existing = groups.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                    if (existing == null)
                        groups.Add(new NominalInsulationPercentage(rValueKey, percentage));
                    else
                        existing.TotalAreaPercentage += percentage;
                }

                AdjustPercentages(decimalPlaceForPercentages, groups);

                return BuildNominalInsulationString(groups);
            }

            return string.Empty;
        }

        public string GetExposedFloorArea(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            List<Components.FloorComponents.Floor> allMyFloors = GetComponentsByType<Components.FloorComponents.Floor>();
            if (allMyFloors != null && allMyFloors.Count > 0)
            {
                allMyFloors = allMyFloors.OrderBy(f => f.Id).ToList();
                
                NominalInsulationPercentage existing = null;
                foreach (var floor in allMyFloors)
                {
                    decimal NominalInsulation = Math.Round(floor.Construction.Type.NominalInsulation, 2);
                    decimal area = Math.Round(floor.Measurements.Area, 2);

                    if (!enumerateAll)
                        existing = groups.Where(nip => nip.NominalInsulation == NominalInsulation).FirstOrDefault();

                    if (existing == null)
                    {
                        NominalInsulation = Math.Round(NominalInsulation * (decimal)5.678, 1); //To convert forcibly into imperial from metric 
                        area = Math.Round(area * (decimal)10.7639, 0); //To convert forcibly into imperial from metric
                        groups.Add(new NominalInsulationPercentage(NominalInsulation, area));
                    }
                    else
                        existing.TotalAreaPercentage += area;
                }

                return BuildNominalInsulationString(groups);
            }

            return string.Empty;
        }

        public string GetExteriorFoundationInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1)
        {
            var basementsAndWalkouts = GetComponentsByType<Foundation>().OrderBy(f => f.Id).Where(f => f is Basement || f is Walkout).ToList();

            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            if (basementsAndWalkouts != null && basementsAndWalkouts.Count > 0)
            {
                basementsAndWalkouts = basementsAndWalkouts.OrderBy(w => w.Id).ToList();
                decimal basementsAndWalkoutsTotalArea = basementsAndWalkouts.Sum(f => GetFoundationTotalArea(f));
                // attention : pony wall included (test case avec pony wall to insure total = 100.0%)

                foreach (var foundation in basementsAndWalkouts)
                {
                    ExposedSurfaces exteriorPortions = foundation.GetExtFndPortions();

                    // Basements and Walkouts
                    decimal area = exteriorPortions.ExteriorAboveGroundArea + exteriorPortions.ExteriorBelowGroundArea;
                    decimal percentage = (basementsAndWalkoutsTotalArea == 0) ? 0 : 100m * area / basementsAndWalkoutsTotalArea;

                    CodeDescriptionAndComposite composite = null;
                    if (foundation is Basement)
                        composite = ((Basement)foundation).Wall.Construction.ExteriorAddedInsulation;
                    else // if (foundation is Walkout)
                        composite = ((Walkout)foundation).Wall.Construction.ExteriorAddedInsulation;

                    // Subdivide this foundation's area to its composite sections
                    composite.CalculateRemainder();

                    // Special cases
                    if (composite.Composite.Count == 0)
                        groups.Add(new NominalInsulationPercentage(0m, percentage));

                    else
                    {
                        foreach (var section in composite.Composite)
                        {
                            decimal nominalR = decimal.Round(Conversions.RSItoR(section.NominalRsi), (int)decimalPlaceForInsulation, MidpointRounding.AwayFromZero);
                            groups.Add(new NominalInsulationPercentage(nominalR, section.Percentage * percentage / 100m));
                        }
                    }
                }

                AdjustPercentages(decimalPlaceForPercentages, groups);

                return BuildNominalInsulationString(groups);
            }
            return string.Empty;
        }

        public string GetFoundationFloorHeaderNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            List<Components.FloorHeaderComponents.FloorHeader> allFloorHeaders = GetComponentsByType<Components.FloorHeaderComponents.FloorHeader>();
            if (allFloorHeaders == null || allFloorHeaders.Count == 0) return string.Empty;

            List<Components.FloorHeaderComponents.FloorHeader> foundationFloorHeaders = allFloorHeaders.OrderBy(f => f.Id).Where(fh => fh.Parent is Basement).ToList();
            if (foundationFloorHeaders != null && foundationFloorHeaders.Count > 0)
            {
                decimal totalArea = foundationFloorHeaders.Sum(fh => fh.Measurements.Area);

                NominalInsulationPercentage existing = null;
                foreach (var floorHeader in foundationFloorHeaders)
                {
                    decimal rValueKey = decimal.Round( Conversions.RSItoR(floorHeader.Construction.Type.NominalInsulation),
                                                       (int)decimalPlaceForInsulation );
                    decimal percentage = 100m * floorHeader.Measurements.Area / totalArea;

                    if (!enumerateAll)
                        existing = groups.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                    if (existing == null)
                        groups.Add(new NominalInsulationPercentage(rValueKey, percentage));
                    else
                        existing.TotalAreaPercentage += percentage;
                }

                AdjustPercentages(decimalPlaceForPercentages, groups);

                return BuildNominalInsulationString(groups);
            }

            return string.Empty;
        }

        public string GetCrawlspaceHeaderNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            List<Components.FloorHeaderComponents.FloorHeader> allFloorHeaders = GetComponentsByType<Components.FloorHeaderComponents.FloorHeader>();
            if (allFloorHeaders == null || allFloorHeaders.Count == 0) return string.Empty;

            List<Components.FloorHeaderComponents.FloorHeader> foundationFloorHeaders = allFloorHeaders.OrderBy(f => f.Id).Where(fh => fh.Parent is Crawlspace).ToList();
            if (foundationFloorHeaders != null && foundationFloorHeaders.Count > 0)
            {
                decimal totalArea = foundationFloorHeaders.Sum(fh => fh.Measurements.Area);

                NominalInsulationPercentage existing = null;
                foreach (var floorHeader in foundationFloorHeaders)
                {
                    decimal rValueKey = decimal.Round(Conversions.RSItoR(floorHeader.Construction.Type.NominalInsulation),
                                                       (int)decimalPlaceForInsulation);
                    decimal percentage = 100m * floorHeader.Measurements.Area / totalArea;

                    if (!enumerateAll)
                        existing = groups.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                    if (existing == null)
                        groups.Add(new NominalInsulationPercentage(rValueKey, percentage));
                    else
                        existing.TotalAreaPercentage += percentage;
                }

                AdjustPercentages(decimalPlaceForPercentages, groups);

                return BuildNominalInsulationString(groups);
            }

            return string.Empty;
        }

        private decimal GetFoundationTotalArea(Foundation foundation)
        {
            ExposedSurfaces exteriorPortions = foundation.GetExtFndPortions();
            return exteriorPortions.ExteriorAboveGroundArea + exteriorPortions.ExteriorBelowGroundArea + exteriorPortions.PonyWallArea;
        }

        public string GetFoundationDefinitions(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1)
        {
            // Make sure we have foundations to work with (ie: not Slabs)
            var basementsAndWalkouts = GetComponentsByType<Foundation>().OrderBy(f => f.Id).Where(f => f is Basement || f is Walkout).ToList();
            var allCrawlspaces = GetComponentsByType<Foundation>().OrderBy(f => f.Id).Where(f => f is Crawlspace).Cast<Crawlspace>().ToList();

            var noSlabs = new List<Foundation>();
            noSlabs.AddRange(basementsAndWalkouts);
            noSlabs.AddRange(allCrawlspaces);

            if (noSlabs.Count() == 0) return string.Empty;

            #region PHASE 1: Group foundations by type
            // Setup the buckets for grouping
            List<NominalInsulationPercentage> noSlabsGroup = new List<NominalInsulationPercentage>();
            List< NominalInsulationPercentage> crawlspaceFloorsGroup = new List<NominalInsulationPercentage>();
            List<NominalInsulationPercentage> ponywallsGroup = new List<NominalInsulationPercentage>();

            // These totals don't vary per loop iteration:
            decimal noSlabsTotalArea = noSlabs.Sum( f => GetFoundationTotalArea(f) );
            decimal floorsTotalArea = (allCrawlspaces == null)? 0m : allCrawlspaces.Sum(c => c.Floor.Measurements.FloorArea);;

            // Go through each foundation to classify the pieces into their correct group
            foreach (var foundation in noSlabs)
            {
                ExposedSurfaces exteriorPortions = foundation.GetExtFndPortions();

                // First group: Basements, Crawlspaces and Walkouts
                decimal area = exteriorPortions.ExteriorAboveGroundArea + exteriorPortions.ExteriorBelowGroundArea;
                decimal percentage = (noSlabsTotalArea == 0)? 0 : 100m * area / noSlabsTotalArea;

                CodeDescriptionAndComposite composite = null;
                if (foundation is Basement)
                    composite = ((Basement)foundation).Wall.Construction.InteriorAddedInsulation;
                else if (foundation is Crawlspace)
                    composite = ((Crawlspace)foundation).Wall.Construction.Type;
                else // if (foundation is Walkout)
                    composite = ((Walkout)foundation).Wall.Construction.InteriorAddedInsulation;

                // Subdivide this foundation's area to its composite sections
                composite.CalculateRemainder();

                // Special cases
                if (composite.Composite.Count == 0)
                    noSlabsGroup.Add(new NominalInsulationPercentage(0m, percentage));

                else
                {
                    foreach (var section in composite.Composite)
                    {
                        decimal nominalR = decimal.Round(Conversions.RSItoR(section.NominalRsi), (int)decimalPlaceForInsulation, MidpointRounding.AwayFromZero);
                        noSlabsGroup.Add(new NominalInsulationPercentage(nominalR, section.Percentage * percentage / 100m));
                    }
                }


                // Crawlspaces have an additional grouping for their FloorsAbove
                if (foundation is Crawlspace)
                {
                    decimal floorPercentage = (floorsTotalArea == 0)? 0 : 100m * ((Crawlspace)foundation).Floor.Measurements.FloorArea / floorsTotalArea;
                    decimal nominalR = decimal.Round(Conversions.RSItoR(((Crawlspace)foundation).Floor.Construction.FloorsAbove.NominalInsulation), (int)decimalPlaceForInsulation, MidpointRounding.AwayFromZero);
                    crawlspaceFloorsGroup.Add(new NominalInsulationPercentage(nominalR, floorPercentage));
                }


                // Third group: PonyWalls
                bool foundationHasPonyWall = (foundation is Basement && ((Basement)foundation).Wall.HasPonyWall)
                                                || (foundation is Walkout && ((Walkout)foundation).Wall.HasPonyWall);

                if (foundationHasPonyWall)
                {
                    decimal ponywallPercentage = (noSlabsTotalArea == 0)? 0 : 100m * exteriorPortions.PonyWallArea / noSlabsTotalArea;

                    if (foundation is Basement)
                        composite = ((Basement)foundation).Wall.Construction.PonyWallType;
                    else if (foundation is Walkout)
                        composite = ((Walkout)foundation).Wall.Construction.PonyWallType;

                    composite.CalculateRemainder();
                    foreach (var section in composite.Composite)
                    {
                        decimal nominalR = decimal.Round(Conversions.RSItoR(section.NominalRsi), (int)decimalPlaceForInsulation, MidpointRounding.AwayFromZero);
                        ponywallsGroup.Add(new NominalInsulationPercentage(nominalR, section.Percentage * ponywallPercentage / 100m));
                    }
                }
            }
            #endregion

            #region PHASE 2: Serialize the groupings to string
            // Round off earch percentage in first and third groups and adjust them to ensure 100% total
            AdjustPercentages(decimalPlaceForPercentages, noSlabsGroup, ponywallsGroup);

            // Serialize first group to output string
            StringBuilder nominalInsulation = new StringBuilder();
            if (noSlabsGroup.Count > 0)
                nominalInsulation.Append( BuildNominalInsulationString(noSlabsGroup) );

            if (crawlspaceFloorsGroup.Count > 0)
            {
                // Add seperator between first and second group if necessary
                if (noSlabsGroup.Count > 0)
                    nominalInsulation.Append(';');

                // Round off earch percentage in second group and adjust to ensure 100% total
                AdjustPercentages(decimalPlaceForPercentages, crawlspaceFloorsGroup);

                // Serialize second group to output string
                nominalInsulation.Append( BuildNominalInsulationString(crawlspaceFloorsGroup) );
            }

            if (ponywallsGroup.Count > 0)
            {
                // Add seperator before third group if necessary
                if (noSlabsGroup.Count > 0 || crawlspaceFloorsGroup.Count > 0)
                    nominalInsulation.Append(';');

                // Serialize third group to output string
                nominalInsulation.Append( BuildNominalInsulationString(ponywallsGroup) );
            }
            #endregion

            // Return serialized string of foundation groupings
            return nominalInsulation.ToString();
        }

        public string GetFoundationTypes()
        {
            var basementsAndWalkouts = GetComponentsByType<Foundation>().OrderBy(f => f.Id).Where(f => f is Basement || f is Walkout).ToList();
            var crawlspaces = GetComponentsByType<Foundation>().OrderBy(f => f.Id).Where(f => f is Crawlspace).ToList();
            if (basementsAndWalkouts.Count + crawlspaces.Count == 0) return string.Empty;

            StringBuilder foundationTypes = new StringBuilder();
            int basementOrWalkoutCounter = 1;
            foreach (var basementOrWalkout in basementsAndWalkouts)
            {
                var interiorWall = basementOrWalkout is Basement?
                    ((Basement)basementOrWalkout).Wall.Construction.InteriorAddedInsulation
                    : ((Walkout)basementOrWalkout).Wall.Construction.InteriorAddedInsulation;

                // Special case
                if (interiorWall.Composite.Count == 0)
                    foundationTypes.Append(string.Format("B{0};", basementOrWalkoutCounter++));

                else
                {
                    foreach (var section in interiorWall.Composite)
                        foundationTypes.Append(string.Format("B{0};", basementOrWalkoutCounter++));
                }
            }

            StringBuilder floorsAbove = new StringBuilder();
            int crawlspaceCounter = 1, floorsAboveCounter = 1;
            foreach (var crawlspace in crawlspaces)
            {
                var interiorWall = ((Crawlspace)crawlspace).Wall.Construction.Type;

                // Special case
                if (interiorWall.Composite.Count == 0)
                    foundationTypes.Append(string.Format("C{0};", crawlspaceCounter++));

                else
                {
                    foreach (var section in interiorWall.Composite)
                    {
                        foundationTypes.Append(string.Format("C{0};", crawlspaceCounter++));
                    }
                }

                floorsAbove.Append(string.Format("F{0};", floorsAboveCounter++));
            }

            foundationTypes.Append(floorsAbove);

            int ponywallCounter = 1;
            foreach (var foundation in basementsAndWalkouts)
            {
                var foundationWall = (foundation is Basement)? ((Basement)foundation).Wall : ((Walkout)foundation).Wall;
                if (foundationWall.HasPonyWall && foundationWall.Construction.PonyWallType != null)
                {
                    foreach (var section in foundationWall.Construction.PonyWallType.Composite)
                        foundationTypes.Append(string.Format("P{0};", ponywallCounter++));
                }
            }

            // Remove any trailing seperator
            if (foundationTypes.Length > 0 && foundationTypes[foundationTypes.Length - 1] == ';')
                foundationTypes.Remove(foundationTypes.Length - 1, 1);

            return foundationTypes.ToString();
        }

        public string GetWallNominalInsulations(uint decimalPlaceForPercentages = 1, uint decimalPlaceForInsulation = 1, bool enumerateAll = true)
        {
            List<NominalInsulationPercentage> groups = new List<NominalInsulationPercentage>();
            List<Components.WallComponents.Wall> allMyWalls = GetComponentsByType<Components.WallComponents.Wall>();
            if (allMyWalls != null && allMyWalls.Count > 0)
            {
                allMyWalls = allMyWalls.OrderBy(w => w.Id).ToList();
                decimal totalArea = allMyWalls.Sum(w => w.Measurements.Area);

                NominalInsulationPercentage existing = null;
                foreach (var wall in allMyWalls)
                {
                    decimal rValueKey = decimal.Round( Conversions.RSItoR(wall.Construction.Type.NominalInsulation),
                                                       (int)decimalPlaceForInsulation );
                    decimal percentage = 100m * wall.Measurements.Area / totalArea;

                    if (!enumerateAll)
                        existing = groups.Where(nip => nip.NominalInsulation == rValueKey).FirstOrDefault();

                    if (existing == null)
                        groups.Add( new NominalInsulationPercentage(rValueKey, percentage) );
                    else
                        existing.TotalAreaPercentage += percentage;
                }

                AdjustPercentages(decimalPlaceForPercentages, groups);

                return BuildNominalInsulationString(groups);
            }

            return string.Empty;
        }
        #endregion

        #region Constructors and Defaults
        public HouseFile()
        {
            SetDefaults();
        }

        /// <summary>
        /// Sets the initial values for all fields
        /// </summary>
        public void SetDefaults()
        {
            // Default values:
            Version = new HouseFileVersion(1, 3); // .h2k spec
            Language = LanguageOptions.English;
        }
        #endregion

        #region Loading of House File
        /// <summary>
        /// Clears the HouseFile of all data
        /// </summary>
        public void Clear()
        {
            Errors.Clear();
            House = new House();
        }

        /// <summary>
        /// Returns the HouseFile to its initial state
        /// </summary>
        public void Reset()
        {
            Clear();
            SetDefaults();
        }

        public bool Load(HouseFile dataToLoad)
        {
            // Sanity check
            if (dataToLoad == null) return false;

            #region Clear any existing house components
            Clear();
            #endregion

            try
            {
                #region Load data from Header section
                this.Language = LanguageOptions.Clone(dataToLoad.Language);
                this.UiUnits = dataToLoad.UiUnits;
                this.Version = new HouseFileVersion(dataToLoad.Version);
                #endregion

                #region Load data from Application section
                this.Application = new Application(dataToLoad.Application);
                #endregion

                #region Load data from Program Information section
                this.Program = (dataToLoad.Program == null)? null : new XElement( dataToLoad.Program );
                this.ProgramInformation = dataToLoad.ProgramInformation.Clone();
                #endregion

                #region Load data from House Components section
                this.House = dataToLoad.House.Clone();

                // Set the defaults values on NatualAirInfiltration
                if (House.NaturalAirInfiltration != null)
                    House.NaturalAirInfiltration.SetDefaults();

                UpdateReferences();
                #endregion

                #region Load data from User Codes section
                this.Codes = (dataToLoad.Codes == null)? null : dataToLoad.Codes.Clone();
                #endregion

                #region Load data from Energy Upgrades section
                EnergyUpgrades = (dataToLoad.EnergyUpgrades == null)? null : dataToLoad.EnergyUpgrades.Clone();
                #endregion

                #region Load data from Fuel Costs section
                this.FuelCosts = dataToLoad.FuelCosts.Clone();
                #endregion

                #region Load data from Results section
                if (dataToLoad.AllResults == null)
                    AllResults = null;
                else
                {
                    AllResults = new List<Results>();
                    foreach (var result in dataToLoad.AllResults)
                        AllResults.Add(result.Clone());
                }
                #endregion
            }
            catch (Exception e)
            {
                //Errors.Add( new HouseFileError( "Loading of data failed!", "Le chargement des données a échoué!"));
                Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }

            // Fire the OnDeserialization() event
            foreach (var component in HouseComponents)
            {
                var callback = component as ISerializationEvents;
                if (callback != null) callback.OnDeserialization();
            }

            if (EnergyUpgrades != null)
            {
                foreach (var component in EnergyUpgrades.Components)
                {
                    var callback = component as ISerializationEvents;
                    if (callback != null) callback.OnDeserialization();
                }
            }

            if (Codes != null) Codes.DereferenceAll(this);

            return true;
        }

        public bool Load(Stream houseFileStream)
        {
            try { return Load( XDocument.Load(houseFileStream) ); }
            catch (Exception e)
            {
                HouseFile.Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }
        }

        public bool LoadFromString(string houseFileXml)
        {
            try { return Load( XDocument.Parse(houseFileXml) ); }
            catch (Exception e)
            {
                HouseFile.Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }
        }

        public bool LoadFromUri(string houseFileUri, string xsdUri = null)
        {
            try { return Load(XDocument.Load(houseFileUri), xsdUri); }
            catch (Exception e)
            {
                HouseFile.Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }
        }

        public bool Load(XDocument houseFileXml, string xsdUri = null)
        {
            try
            {
                // Validate houseFileXml against schema
                bool successfullyParsed = true;
                if (!string.IsNullOrWhiteSpace(xsdUri) && System.IO.File.Exists(xsdUri))
                    successfullyParsed = ValidateAgainstSchema(houseFileXml, xsdUri);

                // Parse the XML to an object hiearchy
                if (successfullyParsed)
                {
#if NO_SERIALIZERS
                    successfullyParsed = Load(XmlDeserializer<HouseFile>(houseFileXml));
#else
                    HouseFile DeserializedHouseFile = (HouseFile)Serializer.Deserialize(houseFileXml.CreateReader());
                    successfullyParsed = Load(DeserializedHouseFile);
#endif
                }

                if (successfullyParsed)
                    Program = (houseFileXml.Root.Element("Program") == null)? null : new XElement( houseFileXml.Root.Element("Program") );

                // If we have results we need to verify their checksums
                XElement allResults = houseFileXml.Root.Element("AllResults");
                if (successfullyParsed && allResults != null
                        && AllResults != null && AllResults.Count > 0)
                {
                    int resultsIndex = 0;

                    // For each Results element under AllResults
                    foreach (XElement results in allResults.Elements("Results"))
                    {
                        if (results.Attribute("sha256") != null)
                        {
                            // Get the hash for these Results
                            string hash = results.Attribute("sha256").Value;

                            // Verify the hash against the XML
                            AllResults[resultsIndex].IsChecksumVerified = results.VerifySha256(hash);
                            if (!AllResults[resultsIndex].IsChecksumVerified)
                            {
                                // Hash verification failed!
                                Errors.Add(new HouseFileError(
                                    "Checksum error on Results #" + (resultsIndex + 1).ToString() + "(Label: " + AllResults[resultsIndex].Labels.English + ")",
                                    "Erreur de checksum sur Results #" + (resultsIndex + 1).ToString() + "(Label: " + AllResults[resultsIndex].Labels.English + ")"));
                            }
                        }
                        else
                        {
                            // Results is missing its sha256 checksum
                            AllResults[resultsIndex].IsChecksumVerified = false;

                            Errors.Add(new HouseFileError(
                                     "Missing checksum on Results #" + (resultsIndex + 1).ToString() + "(Label: " + AllResults[resultsIndex].Labels.English + ")",
                                     "Le checksum manque sur Results #" + (resultsIndex + 1).ToString() + "(Label: " + AllResults[resultsIndex].Labels.English + ")"));
                        }

                        ++resultsIndex;
                    }
                }

                return successfullyParsed;
            }
            catch (Exception e)
            {
                Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }
        }

        private void UpdateReferences(BaseComponent houseComponent)
        {
            // Do we actually have a component to work with?
            if (houseComponent == null) return;

            // Add reference to instance of HouseFile for easy access
            houseComponent.HouseFile = this;

            // Special case: House has a few components as fields
            if (houseComponent is House)
            {
                ((House)houseComponent).BaseLoads.Parent = houseComponent;
                ((House)houseComponent).HeatingCooling.Parent = houseComponent;
                ((House)houseComponent).Generation.Parent = houseComponent;
                ((House)houseComponent).NaturalAirInfiltration.Parent = houseComponent;
                ((House)houseComponent).Temperatures.Parent = houseComponent;
                if (((House)houseComponent).Ventilation != null)
                    ((House)houseComponent).Ventilation.Parent = houseComponent;
            }

            // For each child component
            foreach (Component child in houseComponent.Components)
            {
                // Set this component at the parent to the child
                child.Parent = houseComponent;

                // Recursively process the child component (and its children)
                UpdateReferences(child);
            }
        }

        /// <summary>
        /// Updates the flat list of components
        /// from the current component tree
        /// (ie: tree to flat)
        /// </summary>
        private void UpdateReferences()
        {
            UpdateReferences(House);
        }
        #endregion

        #region Saving of House File
        public bool Save()
        {
            return Save(House.Labels.English + ".h2k");
        }

        public bool Save(string filename)
        {
            // Write XML to file
            XmlTextWriter h2kWriter = null;
            try
            {
                h2kWriter = new XmlTextWriter(filename, System.Text.Encoding.UTF8);
                h2kWriter.Formatting = Formatting.Indented;
                ToXml().WriteTo(h2kWriter);
            }
            catch (Exception e)
            {
                Errors.Add(HouseFileError.CreateErrorFromException(e));
                return false;
            }
            finally { h2kWriter.Close(); }

            return true;
        }

        public override string ToString()
        {
            return Utf8Helper.ToStringWithDeclaration( ToXml() );
        }

        public XDocument ToXml()
        {
            // Fire the OnPreSerialization() event
            foreach (var component in HouseComponents)
            {
                var callback = component as ISerializationEvents;
                if (callback != null) callback.OnPreSerialization();
            }

            if (EnergyUpgrades != null)
            {
                foreach (var component in EnergyUpgrades.Components)
                {
                    var callback = component as ISerializationEvents;
                    if (callback != null) callback.OnPreSerialization();
                }
            }

            // Build code library
            if (Codes == null) Codes = new Codes();
            Codes.RebuildAll(this);
            if (Codes.AllCodes.Count == 0) Codes = null;

            XDocument houseFileXml = new XDocument(
                    new XDeclaration("1.0", "UTF-8", null)
                );

#if NO_SERIALIZERS
            houseFileXml.Add(XmlSerializer<HouseFile>(this));
#else
            using (XmlWriter writer = houseFileXml.CreateWriter())
                Serializer.Serialize(writer, this);
#endif

            //XNamespace xmlns = "xmlns";
            //XAttribute toDel = houseFileXml.Root.Attribute(xmlns + "xsi");
            //if ( toDel != null && string.IsNullOrEmpty(toDel.Value) )
            //    toDel.Remove();

            //toDel = houseFileXml.Root.Attribute(xmlns + "xsd");
            //if (toDel != null && string.IsNullOrEmpty(toDel.Value))
            //    toDel.Remove();

            // Fire the OnPostSerialization() event
            foreach (var component in HouseComponents)
            {
                var callback = component as ISerializationEvents;
                if (callback != null) callback.OnPostSerialization();
            }

            if (EnergyUpgrades != null)
            {
                foreach (var component in EnergyUpgrades.Components)
                {
                    var callback = component as ISerializationEvents;
                    if (callback != null) callback.OnPostSerialization();
                }
            }

            // If we have Results update their checksums
            if ( houseFileXml.Root.Element("AllResults") != null )
            {
                string hash;
                foreach (XElement results in houseFileXml.Root.Element("AllResults").Elements("Results"))
                {
                    hash = results.CalculateSha256();
                    if ( results.Attribute("sha256") == null )
                        results.Add( new XAttribute("sha256", hash) );
                    else
                        results.Attribute("sha256").Value = hash;
                }
            }

            if (Program != null)
                houseFileXml.Root.Add( new XElement(Program) );

            return houseFileXml;
        }
        #endregion

        #region Serialization
        static public string CalculateSha256(XElement xmlToChecksum)
        {
            return xmlToChecksum.CalculateSha256();
        }

        static public T Clone<T>(T source)
        {
            return XmlDeserializer<T>( XmlSerializer<T>(source) );
        }

        public static bool VerifySha256(XElement xmlToChecksum, string hash)
        {
            return xmlToChecksum.VerifySha256(hash);
        }

        static public T XmlDeserializer<T>(XElement serialized)
        {
            return XmlDeserializer<T>(new XDocument(
                    new XDeclaration("1.0", "utf-8", "yes"),
                    serialized
                ));
        }

        static public T XmlDeserializer<T>(XDocument serialized)
        {
            // Sanity check:
            if (serialized == null) return default(T);

            XmlReader reader = serialized.CreateReader();
            try
            {
                XmlSerializer deserializer = new XmlSerializer( typeof(T) );
                T result = (T)deserializer.Deserialize(reader);
                return result;
            }
            catch (Exception e)
            {
                Errors.Add( HouseFileError.CreateErrorFromException(e) );
                return default(T);
            }
            finally
            {
                reader.Close();
            }
        }

        static public void XmlSerializer<T>(string outputFilename, T data)
        {
            XmlSerializer serializer = new XmlSerializer( typeof(T) );
            FileStream fs = new FileStream(outputFilename, FileMode.Create, FileAccess.Write);
            try
            {
                serializer.Serialize(fs, data);
            }
            finally
            {
                fs.Close();
            }
        }

        static public XElement XmlSerializer<T>(T data)
        {
            XDocument serialized = new XDocument();
            XmlSerializer serializer = new XmlSerializer( typeof(T) );

            XmlWriter writer = serialized.CreateWriter();
            try
            {
                serializer.Serialize(writer, data);
            }
            finally
            {
                writer.Close();
            }

            return serialized.Root;
        }


        public HouseFile Clone()
        {
#if NO_SERIALIZERS
            return HouseFile.Clone<HouseFile>(this);
#else
            using (MemoryStream stream = new MemoryStream(1024))
            {
                Serializer.Serialize(stream, this);
                stream.Position = 0;
                return (HouseFile)Serializer.Deserialize(stream);
            }
#endif
        }

#if !NO_SERIALIZERS
        [XmlIgnore]
        public static HouseFileSerializer Serializer
        {
            get
            {
                if (cachedSerializer == null) cachedSerializer = new HouseFileSerializer();
                return cachedSerializer;
            }
        }

        static public HouseFileSerializer cachedSerializer = null;
#endif
        #endregion
    }

    public class Utf8Helper
    {
        // See http://stackoverflow.com/questions/3871738/force-xdocument-to-write-to-string-with-utf-8-encoding
        private class Utf8StringWriter : StringWriter
        {
            // Override the default encoding as C++ expects UTF-8
            public override Encoding Encoding { get { return Encoding.UTF8; } }
        };

        //Helper function because the native XDocument.ToString() doesn't export the Declaration
        public static string ToStringWithDeclaration(XDocument doc)
        {
            // Sanity check:
            if (doc == null) return String.Empty;

            // Ensure we have an XML declaration
            if (doc.Declaration == null)
                doc.Declaration = new XDeclaration("1.0", "UTF-8", null);

            // Serialize XML to UTF-8 string
            string utf8String = string.Empty;
            StringWriter utf8Writer = null;
            try
            {
                utf8Writer = new Utf8StringWriter();
                doc.Save(utf8Writer, SaveOptions.None);
                utf8String = utf8Writer.ToString();
            }
            finally
            {
                utf8Writer.Close();
            }

            // Return XML as UTF-8 string
            return utf8String;
        }
    }
}
