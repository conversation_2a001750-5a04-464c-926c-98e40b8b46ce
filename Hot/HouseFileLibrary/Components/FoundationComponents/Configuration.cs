using System;
using System.Xml.Serialization;

namespace ca.nrcan.gc.OEE.HouseFileLibrary.Components.FoundationComponents
{
    [Serializable]
    public class Configuration
    {
        [XmlAttribute("type")]
        public string Type;

        [XmlAttribute("subtype")]
        public uint Subtype;

        [XmlAttribute("overlap")]
        public decimal Overlap;

        [XmlText]
        public string Text
        {
            get { return String.Format("{0}_{1}", Type, Subtype); }
            set { }
        }

        public Configuration()
        {
        }
    }
}
