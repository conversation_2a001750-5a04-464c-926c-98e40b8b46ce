using System;
using System.Globalization;
using System.Xml.Serialization;

namespace ca.nrcan.gc.OEE.HouseFileLibrary.Components.FoundationComponents
{
    [Serializable,
    XmlInclude( typeof(Basement) ),
    XmlInclude( typeof(Crawlspace) ),
    XmlInclude( typeof(Slab) ),
    XmlInclude( typeof(Walkout) )]
    public class Foundation : Component
    {
        #region Public Properties
        [XmlAttribute("isExposedSurface")]
        public bool IsExposedSurface;

        [XmlAttribute("exposedSurfacePerimeter")]
        public string ExposedSurfacePerimeterHelper
        {
            get
            {
                return (ExposedSurfacePerimeter == null)? null : ((decimal)ExposedSurfacePerimeter).ToString(LanguageOptions.FromCurrentCulture().CultureInfo);
            }

            set
            {
                if ( !String.IsNullOrEmpty(value) )
                {
                    decimal newValue;
                    if ( decimal.TryParse(value, NumberStyles.Number, LanguageOptions.FromCurrentCulture().CultureInfo, out newValue) )
                        ExposedSurfacePerimeter = newValue;
                    else
                        ExposedSurfacePerimeter = null;
                }
                else
                    ExposedSurfacePerimeter = null;
            }
        }

        [XmlIgnore]
        public decimal? ExposedSurfacePerimeter = null;

        [XmlElement("Configuration")]
        public Configuration Configuration = new Configuration();

        [XmlIgnore]
        public decimal InteriorWallCoreRsiValue
        {
            get
            {
                // Ported from CFoundationConfig::GetCoreWallR()
                if (Configuration.Type.Length > 1
                    && (this is Basement || this is Walkout))
                {
                    // Based on the second letter in the Type
                    switch ( Configuration.Type.ToUpper()[1] )
                    {
                        case 'C': return 0.116m;
                        case 'W': return 0.417m;
                        case 'B': return 0.417m;
                        default: return (0.116m + 0.417m)/2m;
                    }
                }

                return 0;
            }
        }

        [XmlIgnore]
        public decimal InteriorWallEffectiveRsiValue
        {
            get
            {
                FoundationWall wall = null;
                if (this is Basement)
                    wall = ( (Basement)this ).Wall;

                else if (this is Walkout)
                    wall = ( (Walkout)this ).Wall;

                if (wall == null)
                    return 0;

                return wall.Construction.InteriorAddedInsulation.EffectiveRsiValue(InteriorWallCoreRsiValue);
            }
        }
        #endregion

        #region UpdateSummary method and helpers
        public ExposedSurfaces UpdateSummary()
        {
            // Replaces the previous calculations - returned values are also valid for old attachment method
            ExposedSurfaces Results = GetExtFndPortions();
	        Results.ExteriorAboveGroundArea += Results.PonyWallArea;

            if (this is Walkout)
            {  // Walkout
                Results.ExteriorAboveGroundArea += Results.WalkoutPerimeter * ((Walkout)this).Measurements.Height;
                Results.InteriorAboveGroundArea = GetAvailWalkoutAGAreaNS() - Results.ExteriorAboveGroundArea;
                Results.InteriorBelowGroundArea = GetAvailWalkoutBGArea() - Results.ExteriorBelowGroundArea;
            }
            else
            {
                // Common foundation parameters used for summary calculation
                bool isRectangular = false;
                decimal floorWidth, floorLength, wallDepth, wallHeight;
                floorWidth = floorLength = wallDepth = wallHeight = 0;

                // Extract common parameters from specific foundation types
                if (this is Basement)
                {
                    isRectangular = ((Basement)this).Floor.Measurements.IsRectangular;
                    floorWidth = ((Basement)this).Floor.Measurements.Width;
                    floorLength = ((Basement)this).Floor.Measurements.Length;
                    wallDepth = ((Basement)this).Wall.Measurements.Depth;
                    wallHeight = ((Basement)this).Wall.Measurements.Height;
                }
                else if (this is Crawlspace)
                {
                    isRectangular = ((Crawlspace)this).Floor.Measurements.IsRectangular;
                    floorWidth = ((Crawlspace)this).Floor.Measurements.Width;
                    floorLength = ((Crawlspace)this).Floor.Measurements.Length;
                    wallDepth = ((Crawlspace)this).Wall.Measurements.Depth;
                    wallHeight = ((Crawlspace)this).Wall.Measurements.Height;
                }
                else if (this is Slab)
                {
                    isRectangular = ((Slab)this).Floor.Measurements.IsRectangular;
                    floorWidth = ((Slab)this).Floor.Measurements.Width;
                    floorLength = ((Slab)this).Floor.Measurements.Length;
                    wallDepth = 1.9m;
                    wallHeight = 2.5m;
                }

                if (isRectangular)
                {   // Rectangular (length/width)
                    Results.InteriorAboveGroundArea = 2 * (floorLength + floorWidth)
                                                        * (wallHeight - wallDepth)
                                                        - Results.ExteriorAboveGroundArea - Results.PonyWallArea;

                    Results.InteriorBelowGroundArea = 2 * (floorLength + floorWidth)
                                                        * wallDepth
                                                        - Results.ExteriorBelowGroundArea;
                }
                else
                {   // Non rectangular (perimeter/area)
                    Results.InteriorAboveGroundArea = floorLength
                                                      * (wallHeight - wallDepth)
                                                      - Results.ExteriorAboveGroundArea - Results.PonyWallArea;

                    Results.InteriorBelowGroundArea = floorLength * wallDepth - Results.ExteriorBelowGroundArea;
                }
            }
	
	        if (Results.InteriorAboveGroundArea < 0) Results.InteriorAboveGroundArea = 0;
	        if (Results.InteriorBelowGroundArea < 0) Results.InteriorBelowGroundArea = 0;

	        Results.ExteriorAboveGroundArea = Decimal.Round(Results.ExteriorAboveGroundArea, 2);
	        Results.ExteriorBelowGroundArea = Decimal.Round(Results.ExteriorBelowGroundArea, 2);
	        Results.InteriorAboveGroundArea = Decimal.Round(Results.InteriorAboveGroundArea, 2);
            Results.InteriorBelowGroundArea = Decimal.Round(Results.InteriorBelowGroundArea, 2);

	        return Results;
        }

        public ExposedSurfaces GetExtFndPortions()
        {
            ExposedSurfaces Results = new ExposedSurfaces();
            
            if (IsExposedSurface)
            {
                decimal exposedPerimeter = ExposedSurfacePerimeter == null ? 0 : (decimal)ExposedSurfacePerimeter;
                if (this is Basement)
                {
                    var wall = ((Basement)this).Wall.Measurements;
                    Results.ExteriorAboveGroundArea = exposedPerimeter * (wall.Height - wall.Depth - wall.PonyWallHeight);
                    Results.PonyWallArea = exposedPerimeter * wall.PonyWallHeight;
                    Results.ExteriorBelowGroundArea = exposedPerimeter * wall.Depth;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = exposedPerimeter;
                }

                else if (this is Crawlspace)
                {
                    var wall = ((Crawlspace)this).Wall.Measurements;
                    Results.ExteriorAboveGroundArea = exposedPerimeter * (wall.Height - wall.Depth);
                    Results.PonyWallArea = 0;
                    Results.ExteriorBelowGroundArea = exposedPerimeter * wall.Depth;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = exposedPerimeter;
                }

                else if (this is Slab)
                {
                    Results.ExteriorAboveGroundArea = 0;
                    Results.PonyWallArea = 0;
                    Results.ExteriorBelowGroundArea = 0;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = exposedPerimeter;
                }

                else if (this is Walkout)
                {
                    var walkout = (Walkout)this;

                    ExteriorSurfaces surfaces;
                    if (walkout.Locations != null)
                    {
                        surfaces = GetExpAreaSimpleWalkout();
                        Results.ExposedPerimeter = walkout.Locations.L1_1.X2 + walkout.Locations.L1_2.X2
                                                 + walkout.Locations.L2_2.X2 + walkout.Locations.L2_1.X2
                                                 - walkout.Locations.L1_1.X1 - walkout.Locations.L1_2.X1
                                                 - walkout.Locations.L2_2.X1 - walkout.Locations.L2_1.X1;
                    }
                    else
                    {
                        surfaces = walkout.ExteriorSurfaces;
                        Results.ExposedPerimeter = 0;
                    }

                    Results.ExteriorAboveGroundArea = surfaces.AboveGradeArea;
                    Results.PonyWallArea = surfaces.PonyWallArea;
                    Results.ExteriorBelowGroundArea = surfaces.BelowGradeArea;
                    Results.WalkoutPerimeter = surfaces.SlabPerimeter;
                }
            }
            else// IsExposedSurface == false
            {   // Old attachment method => no description of PonyWall attachments
                Results.PonyWallArea = 0;

                decimal attAGArea = 0, attBGArea = 0, attSlabLength = 0, perimeter = 0;

                // ported from GetAreaAttach()
                if (HouseFile.House.FoundationAttachments != null)
                {
                    foreach (var attachment in HouseFile.House.FoundationAttachments)
                    {
                        if (attachment.Foundation2 != null)
                        {
                            if (attachment.Foundation1.Id == this.Id)
                            {
                                attAGArea += attachment.Foundation1.AboveGradeArea;
                                attBGArea += attachment.Foundation1.BelowGradeArea;
                                attSlabLength += attachment.Foundation1.SlabLength;
                            }
                            else if (attachment.Foundation2.Id == this.Id)
                            {
                                attAGArea += attachment.Foundation2.AboveGradeArea;
                                attBGArea += attachment.Foundation2.BelowGradeArea;
                                attSlabLength += attachment.Foundation2.SlabLength;
                            }
                        }
                    }
                }

                if (this is Basement)
                {
                    var floor = ((Basement)this).Floor.Measurements;
                    if (floor.IsRectangular)
                        perimeter = 2 * (floor.Length + floor.Width); //length & Width
                    else
                        perimeter = floor.Perimeter; //perimeter & area

                    var wall = ((Basement)this).Wall.Measurements;
                    Results.ExteriorAboveGroundArea = perimeter * (wall.Height - wall.Depth) - attAGArea;
                    Results.ExteriorBelowGroundArea = perimeter * wall.Depth - attBGArea;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = Results.ExteriorBelowGroundArea / wall.Depth;
                }

                else if (this is Crawlspace)
                {
                    var floor = ((Crawlspace)this).Floor.Measurements;
                    if (floor.IsRectangular)
                        perimeter = 2 * (floor.Length + floor.Width); //length & Width
                    else
                        perimeter = floor.Perimeter; //perimeter & area
                    
                    var wall = ((Crawlspace)this).Wall.Measurements;
                    Results.ExteriorAboveGroundArea = perimeter * wall.Height - attAGArea;
                    Results.ExteriorBelowGroundArea = perimeter * wall.Depth - attBGArea;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = Results.ExteriorAboveGroundArea / wall.Height;
                }

                else if (this is Slab)
                {
                    var floor = ((Slab)this).Floor.Measurements;
                    if (floor.IsRectangular)
                        perimeter = 2 * (floor.Length + floor.Width); //length & Width
                    else
                        perimeter = floor.Perimeter; //perimeter & area

                    Results.ExteriorAboveGroundArea = 0;
                    Results.ExteriorBelowGroundArea = 0;
                    Results.WalkoutPerimeter = 0;
                    Results.ExposedPerimeter = perimeter - attSlabLength;
                }

                else if (this is Walkout)
                {
                    var walkout = ((Walkout)this).Measurements;
                    decimal availableWalkoutSlabPerimeter = 0;
                    if (walkout.WithSlab)
                        availableWalkoutSlabPerimeter = 2 * walkout.L4 + walkout.L2; // ported from GetAvailWalkoutSlabPer()

                    decimal availableAboveGroundArea = GetAvailWalkoutAGAreaNS();
                    if (walkout.WithSlab) // ported from GetAvailWalkoutAGArea()
                        availableAboveGroundArea -= ((Walkout)this).Wall.Measurements.Height * availableWalkoutSlabPerimeter; // ported from GetAvailWalkoutAGAreaWS()

                    Results.ExteriorAboveGroundArea = availableAboveGroundArea - attAGArea;
                    Results.ExteriorBelowGroundArea = GetAvailWalkoutBGArea() - attBGArea;
                    Results.WalkoutPerimeter = availableWalkoutSlabPerimeter - attSlabLength;
                    Results.ExposedPerimeter = 0;
                }
            }

            return Results;
        }

        //  Calculates the available above grade area of a walkout (including the slab portion if applicable)
        decimal GetAvailWalkoutAGAreaNS()
        {
            var walkout = (Walkout)this;

            var d1 = walkout.Measurements.D1;
            var d2 = walkout.Measurements.D2;
            var d3 = walkout.Measurements.D3;
            var d4 = walkout.Measurements.D4;

            var l1 = walkout.Measurements.L1;
            var l2 = walkout.Measurements.L2;

            var hasPW = walkout.Wall.HasPonyWall;

            decimal AGAreaL11, BGAreaL11, AGAreaL12, BGAreaL12, AGAreaL21, BGAreaL21, AGAreaL22, BGAreaL22;

            // first L1 section
            GetAreaL1(d1, d2, 0, l1, out AGAreaL11, out BGAreaL11);

            //second L1 section
            if (hasPW)   //Pony Wall, no d3 and d4
                GetAreaL1(d1, d2, 0, l1, out AGAreaL12, out BGAreaL12);
            else
                GetAreaL1(d4, d3, 0, l1, out AGAreaL12, out BGAreaL12);

            // first L2 section
            if (walkout.Measurements.WithSlab)
            {
                BGAreaL21 = 0;
                AGAreaL21 = l2 * walkout.Measurements.Height;
            }
            else
            {
                if (hasPW)   //Pony Wall, no d3 and d4
                    GetAreaL2(d2, d2, 0, l2, out AGAreaL21, out BGAreaL21);
                else
                    GetAreaL2(d2, d3, 0, l2, out AGAreaL21, out BGAreaL21);
            }

            // second L2 section
            if (hasPW)   //Pony Wall, no d3 and d4
                GetAreaL2(d1, d1, 0, l2, out AGAreaL22, out BGAreaL22);
            else
                GetAreaL2(d1, d4, 0, l2, out AGAreaL22, out BGAreaL22);

            // sum of all 4 sections    
            return (AGAreaL11 + AGAreaL12 + AGAreaL21 + AGAreaL22);
        }

        //  Calculates the available below grade area of a walkout
        decimal GetAvailWalkoutBGArea()
        {
            decimal AGAreaL11, BGAreaL11, AGAreaL12, BGAreaL12, AGAreaL21, BGAreaL21, AGAreaL22, BGAreaL22;

            var walkout = (Walkout)this;
            var d1 = walkout.Measurements.D1;
            var d2 = walkout.Measurements.D2;
            var d3 = walkout.Measurements.D3;
            var d4 = walkout.Measurements.D4;
            var l1 = walkout.Measurements.L1;
            var l2 = walkout.Measurements.L2;
            var hasPW = walkout.Wall.HasPonyWall;

            // first L1 section
            GetAreaL1(d1, d2, 0, l1, out AGAreaL11, out BGAreaL11);

            // second L1 section
            if (hasPW)   //Pony Wall, no d3 and d4
                GetAreaL1(d1, d2, 0, l1, out AGAreaL12, out BGAreaL12);
            else
                GetAreaL1(d4, d3, 0, l1, out AGAreaL12, out BGAreaL12);

            // first L2 section
            if (walkout.Measurements.WithSlab)
            {
                BGAreaL21 = 0;
                AGAreaL21 = l2 * walkout.Measurements.Height;
            }
            else
            {
                if (hasPW)   //Pony Wall, no d3 and d4
                    GetAreaL2(d2, d2, 0, l2, out AGAreaL21, out BGAreaL21);
                else
                    GetAreaL2(d2, d3, 0, l2, out AGAreaL21, out BGAreaL21);
            }

            // second L2 section
            if (hasPW)   //Pony Wall, no d3 and d4
                GetAreaL2(d1, d1, 0, l2, out AGAreaL22, out BGAreaL22);
            else
                GetAreaL2(d1, d4, 0, l2, out AGAreaL22, out BGAreaL22);

            // sum of all 4 sections
            return (BGAreaL11 + BGAreaL12 + BGAreaL21 + BGAreaL22);
        }

        //  Calculates the area of the L1 side of a walkout
        void GetAreaL1(decimal d1, decimal d2, decimal val_x1, decimal val_x2, out decimal expAGAreaL1, out decimal expBGAreaL1)
        {
            decimal ARect, ASlop; // ARec = BG area in the square 0-L3 section, ASlop = BG area in the section where the ground is slopped
            decimal H1, H2; // height of the grade line at x1 and x2
            decimal x1, x2; // positions

            // area L1 portion
            if (val_x1 == val_x2)
            {
                expBGAreaL1 = 0;
                expAGAreaL1 = 0;
            }
            else
            {
                var walkout = (Walkout)this;
                var l1 = walkout.Measurements.L1;
                var l3 = walkout.Measurements.L3;
                var l4 = walkout.Measurements.L4;

                ARect = d1 * (Math.Min(l3, val_x2) - Math.Min(l3, val_x1));  // section (0 - L3)

                // section under the diagonal
                x1 = Math.Max(l3, val_x1);    // part of x1 from (0 - L3) already accounted for

                if (walkout.Measurements.WithSlab)
                {
                    if (l1 - l3 - l4 == 0 || val_x2 < l3)  // l1=l3+l4 or x2 on l3
                        ASlop = 0;
                    else
                    {
                        x2 = Math.Min(val_x2, l1 - l4);   // part of x2 on the slab does not contribute to BG area
                        if (x1 > l1 - l4)  // x1 on the slab -> all area on the slab
                            ASlop = 0;
                        else if (x2 < l3)  // x2 before the start of the diagonal -> all area in L3 section
                            ASlop = 0;
                        else    // section on the diagonal
                        {
                            H1 = d1 * (l1 - l4 - x1) / (l1 - l4 - l3);  // height on the diagonal at x1 position
                            H2 = d1 * (l1 - l4 - x2) / (l1 - l4 - l3);  // height on the diagonal at x2 position
                            ASlop = (H1 + H2) / 2 * (x2 - x1);
                        }
                    }
                }
                else
                {
                    if (l1 - l3 == 0 || val_x2 < l3)  // l1=l3 or x2 on l3
                        ASlop = 0;
                    else
                    {
                        x2 = val_x2;
                        H1 = d1 - (d1 - d2) * (x1 - l3) / (l1 - l3);  // height on the diagonal at x1 position
                        H2 = d1 - (d1 - d2) * (x2 - l3) / (l1 - l3);  // height on the diagonal at x2 position
                        ASlop = (H1 + H2) / 2 * (x2 - x1);
                    }
                }
                expBGAreaL1 = ARect + ASlop;
                expAGAreaL1 = walkout.Measurements.Height * (val_x2 - val_x1) - expBGAreaL1;   // AG = area of the (x1-x2) rectangle - BG
            }
        }

        //  Calculates the area of the L2 side of a walkout
        void GetAreaL2(decimal d2, decimal d3, decimal val_x1, decimal val_x2, out decimal expAGAreaL2, out decimal expBGAreaL2)
        {
            decimal H1, H2; // height of the grade line at x1 and x2

            // area L2 portion
            var walkout = (Walkout)this;
            var l2 = walkout.Measurements.L2;
            if (l2 == 0)
                H1 = H2 = 0;
            else
            {
                H1 = d2 - (d2 - d3) * val_x1 / l2;
                H2 = d2 - (d2 - d3) * val_x2 / l2;
            }

            expBGAreaL2 = (H1 + H2) / 2 * (val_x2 - val_x1);
            expAGAreaL2 = walkout.Measurements.Height * (val_x2 - val_x1) - expBGAreaL2; // AG = area of the (x1-x2) rectangle - BG
        }

        // evaluates walkout areas for the simple method
        ExteriorSurfaces GetExpAreaSimpleWalkout()
        {
            ExteriorSurfaces surfaces = new ExteriorSurfaces();

            decimal expAGAreaL11, expAGAreaL12, expAGAreaL21, expAGAreaL22,
                    expBGAreaL11, expBGAreaL12, expBGAreaL21, expBGAreaL22,
                    expPWAreaL11, expPWAreaL12, expPWAreaL21, expPWAreaL22;

            var walkout = (Walkout)this;

            var d1 = walkout.Measurements.D1;
            var d2 = walkout.Measurements.D2;
            var d3 = walkout.Measurements.D3;
            var d4 = walkout.Measurements.D4;
            var d5 = walkout.Measurements.D5;

            var l1 = walkout.Measurements.L1;
            var l4 = walkout.Measurements.L4;

            var hasPW = walkout.Wall.HasPonyWall;
            var hasSlab = walkout.Measurements.WithSlab;

            var l11_x1 = walkout.Locations.L1_1.X1;
            var l11_x2 = walkout.Locations.L1_1.X2;
            var l12_x1 = walkout.Locations.L1_2.X1;
            var l12_x2 = walkout.Locations.L1_2.X2;

            var l21_x1 = walkout.Locations.L2_1.X1;
            var l21_x2 = walkout.Locations.L2_1.X2;
            var l22_x1 = walkout.Locations.L2_2.X1;
            var l22_x2 = walkout.Locations.L2_2.X2;

            // first L1 area
            if (hasSlab)  //has a slab, do not take into acount the exp area above it
                GetAreaL1(d1, d2, Math.Min(l1 - l4, l11_x1), Math.Min(l1 - l4, l11_x2), out expAGAreaL11, out expBGAreaL11);
            else
                GetAreaL1(d1, d2, l11_x1, l11_x2, out expAGAreaL11, out expBGAreaL11);

            if (hasPW)
            {
                if (hasSlab)  //has a slab, do not take into acount the exp area above it
                    expPWAreaL11 = expAGAreaL11 - (Math.Min(l1 - l4, l11_x2) - Math.Min(l1 - l4, l11_x1)) * d5;
                else
                    expPWAreaL11 = expAGAreaL11 - (l11_x2 - l11_x1) * d5;
            }
            else
                expPWAreaL11 = 0;
            expAGAreaL11 -= expPWAreaL11;

            // second L1 area
            if (hasPW)    // pony wall => no d4 and d3 (d1 and d2)
                if (hasSlab)  //has a slab, do not take into acount the exp area above it
                {
                    GetAreaL1(d1, d2, Math.Min(l1 - l4, l12_x1), Math.Min(l1 - l4, l12_x2), out expAGAreaL12, out expBGAreaL12);
                    expPWAreaL12 = expAGAreaL12 - (Math.Min(l1 - l4, l12_x2) - Math.Min(l1 - l4, l12_x1)) * d5;
                }
                else
                {
                    GetAreaL1(d1, d2, l12_x1, l12_x2, out expAGAreaL12, out expBGAreaL12);
                    expPWAreaL12 = expAGAreaL12 - (l12_x2 - l12_x1) * d5;
                }
            else
            {
                if (hasSlab)  //has a slab, do not take into acount the exp area above it
                    GetAreaL1(d4, d3, Math.Min(l1 - l4, l12_x1), Math.Min(l1 - l4, l12_x2), out expAGAreaL12, out expBGAreaL12);
                else
                    GetAreaL1(d4, d3, l12_x1, l12_x2, out expAGAreaL12, out expBGAreaL12);
                expPWAreaL12 = 0;
            }
            expAGAreaL12 -= expPWAreaL12;

            // first L2 area
            if (hasSlab) // slab
            {
                expBGAreaL21 = 0;
                expAGAreaL21 = 0;
                expPWAreaL21 = 0;
            }
            else    // slab, this exposed area is already accounted for in the exposed perimeter
            {
                if (hasPW)    // pony wall => no d4 and d3 (d1 and d2)
                {
                    GetAreaL2(d2, d2, l21_x1, l21_x2, out expAGAreaL21, out expBGAreaL21);
                    expPWAreaL21 = expAGAreaL21 - (l21_x2 - l21_x1) * d5;
                }
                else
                {
                    GetAreaL2(d2, d3, l21_x1, l21_x2, out expAGAreaL21, out expBGAreaL21);
                    expPWAreaL21 = 0;
                }
            }
            expAGAreaL21 -= expPWAreaL21;

            // second L2 area
            if (hasPW)    // pony wall => no d4 and d3 (d1 and d2)
            {
                GetAreaL2(d1, d1, l22_x1, l22_x2, out expAGAreaL22, out expBGAreaL22);
                expPWAreaL22 = expAGAreaL22 - (l22_x2 - l22_x1) * d5;
            }
            else
            {
                GetAreaL2(d1, d4, l22_x1, l22_x2, out expAGAreaL22, out expBGAreaL22);
                expPWAreaL22 = 0;
            }
            expAGAreaL22 -= expPWAreaL22;

            // get total area for all 4 faces and initialize the variables to be sent to the core
            surfaces.AboveGradeArea = expAGAreaL11 + expAGAreaL12 + expAGAreaL21 + expAGAreaL22;
            surfaces.BelowGradeArea = expBGAreaL11 + expBGAreaL12 + expBGAreaL21 + expBGAreaL22;
            surfaces.PonyWallArea = expPWAreaL11 + expPWAreaL12 + expPWAreaL21 + expPWAreaL22;

            if (hasSlab)
            {
                // portion on L2(1)
                surfaces.SlabPerimeter = l21_x2 - l21_x1;
                
                // portion on L1(1)
                if (l11_x2 > l1 - l4)   // part of the exposed per is on the slab
                    if (l11_x2 > l1 - l4)  // exposed per entirely on the slab
                        surfaces.SlabPerimeter += l11_x2 - l11_x2;
                    else  // exposed per partly on the slab
                        surfaces.SlabPerimeter += l11_x2 - (l1 - l4);
                
                // portion on L1(2)
                if (l12_x2 > l1 - l4)   // part of the exposed per is on the slab
                    if (l12_x1 > l1 - l4)  // exposed per entirely on the slab
                        surfaces.SlabPerimeter += l12_x2 - l12_x1;
                    else  // exposed per partly on the slab
                        surfaces.SlabPerimeter += l12_x2 - (l1 - l4);
            }
            else
                surfaces.SlabPerimeter = 0;

            return surfaces;
        }
        #endregion

        #region Constructors and Defaults
        public Foundation()
        {
            Label = "Foundation";
        }
        #endregion
    }
}
