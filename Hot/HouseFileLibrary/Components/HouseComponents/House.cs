using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;
using ca.nrcan.gc.OEE.HouseFileLibrary.H2kResources;
#if !NO_SERIALIZERS
using Microsoft.Xml.Serialization.GeneratedAssembly;
#endif

namespace ca.nrcan.gc.OEE.HouseFileLibrary.Components.HouseComponents
{
    [Serializable]
    public class House : BaseComponent
    {
        #region "Public Properties"
        [XmlAttribute("id")]
        public new uint Id
        {
            get { return 0; }
            set { }
        }

        [XmlAttribute("code")]
        public String Code;

        [XmlElement("Labels")]
        public Labels Labels = new Labels();

        [XmlElement("Specifications")]
        public SpecificationsComponents.Specifications Specifications = new SpecificationsComponents.Specifications();

        [XmlElement("WindowTightness")]

		#region WindowTightness
		public CodeTextAndValue WindowTightnessXml
		{
			get { return (CodeTextAndValue)WindowTightness; }
			set
			{
				if (value == null) WindowTightness = null;
				else
				{
					WindowTightness = WindowAirTightness.All.Where(dt => dt.Code.ToString() == value.Code).FirstOrDefault();
					if (WindowTightness.IsUserSpecified)
						WindowTightness.Value = value.Value;
				}
			}
		}

		[XmlIgnore]
		public WindowAirTightness WindowTightness
		{
			get { return WindowTightness_; }
			set
			{
				if (value == null)
					WindowTightness_ = WindowAirTightness.CsaA1;

				else if (value != null && value.IsUserSpecified)
				{
					WindowTightness_ = WindowAirTightness.All.Where(us => us.Code == value.Code).FirstOrDefault();
					WindowTightness_.Value = value.Value;
				}
				else
					WindowTightness_ = value;
			}
		}
		WindowAirTightness WindowTightness_ = WindowAirTightness.CsaA1;
		#endregion

        [XmlElement("BaseLoads")]
        public BaseLoadsComponents.BaseLoads BaseLoads = new BaseLoadsComponents.BaseLoads();

        [XmlElement("Generation")]
        public GenerationComponents.Generation Generation = new GenerationComponents.Generation();

        [XmlElement("HeatingCooling")]
        public HeatingCoolingComponents.HeatingCooling HeatingCooling = new HeatingCoolingComponents.HeatingCooling();

        [XmlArray("FoundationAttachments"), XmlArrayItem("Attachment", typeof(Attachment))]
        public List<Attachment> FoundationAttachments;

        [XmlElement("NaturalAirInfiltration")]
        public NaturalAirInfiltrationComponents.NaturalAirInfiltration NaturalAirInfiltration = new NaturalAirInfiltrationComponents.NaturalAirInfiltration();
        
        [XmlElement("Temperatures")]
        public Temperatures Temperatures = new Temperatures();

        [XmlElement("Ventilation")]
        public VentilationComponents.Ventilation Ventilation;
        #endregion

        #region Constructors and Defaults
        public House()
        {
            SetDefaults();
        }

        public void SetDefaults()
        {
            // Default values:
            Id = 0;
            Labels.English = "House";
            Labels.French = "Maison";
        }
        #endregion

        public House Clone()
        {
#if NO_SERIALIZERS
			return HouseFile.Clone<House>(this);
#else
            using (MemoryStream stream = new MemoryStream(1024))
            {
                Serializer.Serialize(stream, this);
				stream.Position = 0;
                return (House)Serializer.Deserialize(stream);
            }
#endif
        }

#if !NO_SERIALIZERS
        [XmlIgnore]
        public static HouseSerializer Serializer
        {
            get
            {
                if (cachedSerializer == null) cachedSerializer = new HouseSerializer();
                return cachedSerializer;
            }
        }

        private static HouseSerializer cachedSerializer = null;
#endif
    }
}
