// Example: API Controller for ExposedSurfaces

using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using FoundationService.Core.Models;
using FoundationService.API.Models;
using FoundationService.Infrastructure.Repositories;

namespace FoundationService.API.Controllers
{
    [ApiController]
    [Route("api/foundation/exposed-surfaces")]
    public class ExposedSurfacesController : ControllerBase
    {
        private readonly IExposedSurfacesRepository _repository;
        private readonly IMapper _mapper;
        private readonly ILogger<ExposedSurfacesController> _logger;

        public ExposedSurfacesController(
            IExposedSurfacesRepository repository,
            IMapper mapper,
            ILogger<ExposedSurfacesController> logger)
        {
            _repository = repository;
            _mapper = mapper;
            _logger = logger;
        }

        // GET: api/foundation/exposed-surfaces/{id}
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ExposedSurfacesDto>> GetById(Guid id)
        {
            _logger.LogInformation("Getting exposed surfaces with ID: {Id}", id);

            var exposedSurfaces = await _repository.GetByIdAsync(id);
            if (exposedSurfaces == null)
            {
                return NotFound();
            }

            var dto = _mapper.Map<ExposedSurfacesDto>(exposedSurfaces);
            return Ok(dto);
        }

        // GET: api/foundation/exposed-surfaces
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<ExposedSurfacesDto>>> GetAll()
        {
            _logger.LogInformation("Getting all exposed surfaces");

            var exposedSurfaces = await _repository.GetAllAsync();
            var dtos = _mapper.Map<List<ExposedSurfacesDto>>(exposedSurfaces);

            return Ok(dtos);
        }

        // POST: api/foundation/exposed-surfaces
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ExposedSurfacesDto>> Create([FromBody] ExposedSurfacesDto dto)
        {
            if (dto == null)
            {
                return BadRequest("Exposed surfaces data is required");
            }

            _logger.LogInformation("Creating new exposed surfaces");

            var exposedSurfaces = _mapper.Map<ExposedSurfaces>(dto);
            var created = await _repository.AddAsync(exposedSurfaces);
            var createdDto = _mapper.Map<ExposedSurfacesDto>(created);

            return CreatedAtAction(
                nameof(GetById),
                new { id = createdDto.Id },
                createdDto);
        }

        // PUT: api/foundation/exposed-surfaces/{id}
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Update(Guid id, [FromBody] ExposedSurfacesDto dto)
        {
            if (dto == null || id != dto.Id)
            {
                return BadRequest();
            }

            _logger.LogInformation("Updating exposed surfaces with ID: {Id}", id);

            try
            {
                var exposedSurfaces = _mapper.Map<ExposedSurfaces>(dto);
                await _repository.UpdateAsync(exposedSurfaces);
                return NoContent();
            }
            catch (ArgumentException)
            {
                return NotFound();
            }
        }

        // DELETE: api/foundation/exposed-surfaces/{id}
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Delete(Guid id)
        {
            _logger.LogInformation("Deleting exposed surfaces with ID: {Id}", id);

            try
            {
                await _repository.DeleteAsync(id);
                return NoContent();
            }
            catch (ArgumentException)
            {
                return NotFound();
            }
        }
    }
}

// Example API calls:

// POST /api/foundation/exposed-surfaces
// {
//   "exteriorAboveGroundArea": 45.2,
//   "exteriorBelowGroundArea": 32.1,
//   "interiorAboveGroundArea": 0,
//   "interiorBelowGroundArea": 0,
//   "ponyWallArea": 15.3,
//   "walkoutPerimeter": 0,
//   "exposedPerimeter": 25.5
// }

// PUT /api/foundation/exposed-surfaces/{id}
// {
//   "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
//   "exteriorAboveGroundArea": 50.0,
//   "exteriorBelowGroundArea": 35.0,
//   "interiorAboveGroundArea": 0,
//   "interiorBelowGroundArea": 0,
//   "ponyWallArea": 18.0,
//   "walkoutPerimeter": 0,
//   "exposedPerimeter": 28.0
// }
