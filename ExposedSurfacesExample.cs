// Example: How to save ExposedSurfaces data through Foundation calculations

// 1. Create a Basement with exposed surface settings
var basement = new Basement
{
    HouseId = Guid.NewGuid(),
    Label = "Exposed Basement",
    IsExposedSurface = true,  // Enable exposed surface calculations
    ExposedSurfacePerimeter = 25.5m,  // Set the exposed perimeter
    
    // Set up wall measurements (required for calculations)
    Wall = new FoundationWall
    {
        Measurements = new FoundationWallMeasurements
        {
            Height = 2.4m,
            Depth = 1.8m,
            PonyWallHeight = 0.6m
        }
    }
};

// 2. Calculate exposed surfaces
var exposedSurfaces = basement.GetExtFndPortions();

// 3. Save the basement (this will save the foundation data)
var savedBasement = await basementService.AddBasementAsync(basement);

// 4. If you need to save the calculation results to ExposedSurfaces table,
// you would need to add a repository method (see Approach 2 below)

// Example API call to create a basement with exposed surfaces:
// POST /api/foundation/basements
{
  "houseId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "label": "Exposed Basement",
  "isExposedSurface": true,
  "exposedSurfacePerimeter": 25.5,
  "wall": {
    "measurements": {
      "height": 2.4,
      "depth": 1.8,
      "ponyWallHeight": 0.6
    }
  }
}
