services:
  hot2k-gateway:
    build:
      context: ./Hot2K.Gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # Main gateway port - single entry point
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceEndpoints__HouseFileService=http://housefile-api:8080/api/houses
      - ServiceEndpoints__WeatherService=http://weather-api:8080/api/weatherlibrary
      - ServiceEndpoints__EnvelopeService=http://envelope-api:8080/api/envelope
      - ServiceEndpoints__BaseLoadService=http://baseload-api:8080/api/baseload
      - ServiceEndpoints__HvacService=http://hvac-api:8080/api/heatingcooling
      - ServiceEndpoints__HotWaterService=http://hotwater-api:8080/api/hotwater
      - ServiceEndpoints__VentilationService=http://ventilation-api:8080/api/ventilation
      - ServiceEndpoints__AirInfiltrationService=http://airinfiltration-api:8080/api/airinfiltration
      - ServiceEndpoints__FoundationService=http://foundation-api:8080/api/foundation
      - ServiceEndpoints__TemperatureService=http://temperature-api:8080/api/temperature
      - ServiceEndpoints__GenerationService=http://generation-api:8080/api/generation
      - ServiceEndpoints__SimulationEngineService=http://simulation-api:8080/api/simulations
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
      weather-api:
        condition: service_started
      envelope-api:
        condition: service_started
      baseload-api:
        condition: service_started
      hvac-api:
        condition: service_started
      hotwater-api:
        condition: service_started
      foundation-api:
        condition: service_started
      ventilation-api:
        condition: service_started
      airinfiltration-api:
        condition: service_started
      temperature-api:
        condition: service_started
      generation-api:
        condition: service_started
      simulation-api:
        condition: service_started
    volumes:
      - gateway-logs:/app/logs
    networks:
      - hot2k-network
    restart: unless-stopped

  # Auth Service UI
  auth-ui:
    build:
      context: ./AuthService
      dockerfile: Dockerfile
    ports:
      - "5001:80"
    networks:
      - hot2k-network
    restart: unless-stopped

  # Single SQL Server for all services
  sql-server:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=************
      - MSSQL_MEMORY_LIMIT_MB=3072
    ports:
      - "1433:1433"
    volumes:
      - sql-data:/var/opt/mssql
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 6G
    networks:
      - hot2k-network
    restart: unless-stopped

  # House File Service - Internal only (removed external port)
  housefile-api:
    build:
      context: ./HouseFileService
      dockerfile: Dockerfile
    ports:
      - "5005:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Weather Service - Internal only
  weather-api:
    build:
      context: ./WeatherService
      dockerfile: Dockerfile
    ports:
      - "5006:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Envelope Service - Internal only - UPDATED BUILD CONTEXT
  envelope-api:
    build:
      context: .
      dockerfile: ./EnvelopeService/Dockerfile
    ports:
      - "5008:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # BaseLoad Service - Internal only
  baseload-api:
    build:
      context: .
      dockerfile: ./BaseLoadService/Dockerfile
    ports:
      - "5010:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - LD_LIBRARY_PATH=/app/lib
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    volumes:
      - simulation-lib-volume:/app/lib
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # HVAC Service - Internal only
  hvac-api:
    build:
      context: .
      dockerfile: ./HvacService/Dockerfile
    ports:
      - "5011:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Hot Water Service - Internal only
  hotwater-api:
    build:
      context: .
      dockerfile: ./HotWaterService/Dockerfile
    ports:
      - "5012:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Foundation Service - Internal only
  foundation-api:
    build:
      context: .
      dockerfile: ./FoundationService/Dockerfile
    ports:
      - "5017:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;MultipleActiveResultSets=true
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Ventilation Service - Internal only
  ventilation-api:
    build:
      context: .
      dockerfile: ./VentilationService/Dockerfile
    ports:
      - "5014:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Air Infiltration Service - Internal only
  airinfiltration-api:
    build:
      context: .
      dockerfile: ./AirInfiltrationService/Dockerfile
    ports:
      - "5013:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Temperature Service - Internal only
  temperature-api:
    build:
      context: .
      dockerfile: ./TemperatureService/Dockerfile
    ports:
      - "5016:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Generation Service - Internal only
  generation-api:
    build:
      context: .
      dockerfile: ./GenerationService/Dockerfile
    ports:
      - "5015:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

  # Simulation Engine Service - Internal only
  simulation-api:
    build:
      context: ./SimulationEngineService
      dockerfile: Dockerfile
    ports:
      - "5007:8080"
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;
      - ServiceUrls__HouseFileService=http://housefile-api:8080
      - ServiceUrls__WeatherService=http://weather-api:8080
      - ServiceUrls__EnvelopeService=http://envelope-api:8080
      - ServiceUrls__BaseLoadService=http://baseload-api:8080
      - ServiceUrls__HvacService=http://hvac-api:8080
      - ServiceUrls__HotWaterService=http://hotwater-api:8080
      - ServiceUrls__VentilationService=http://ventilation-api:8080
      - ServiceUrls__AirInfiltrationService=http://airinfiltration-api:8080
      - ServiceUrls__FoundationService=http://foundation-api:8080
      - ServiceUrls__TemperatureService=http://temperature-api:8080
      - ServiceUrls__GenerationService=http://generation-api:8080
    volumes:
      - simulation-lib-volume:/app/lib
    depends_on:
      sql-server:
        condition: service_started
      housefile-api:
        condition: service_started
      weather-api:
        condition: service_started
      envelope-api:
        condition: service_started
      baseload-api:
        condition: service_started
      hvac-api:
        condition: service_started
      hotwater-api:
        condition: service_started
      ventilation-api:
        condition: service_started
      airinfiltration-api:
        condition: service_started
      foundation-api:
        condition: service_started
      generation-api:
        condition: service_started
    networks:
      - hot2k-network
    restart: unless-stopped

volumes:
  sql-data:
  simulation-lib-volume:
  gateway-logs:  # New volume for gateway logs

networks:
  hot2k-network:
    driver: bridge