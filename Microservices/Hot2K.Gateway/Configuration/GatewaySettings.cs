
// Updated ServiceEndpoints.cs with User Service
namespace Hot2K.Gateway.Configuration
{
    public class ServiceEndpoints
    {
        public string HouseFileService { get; set; } = string.Empty;
        public string WeatherService { get; set; } = string.Empty;
        public string EnvelopeService { get; set; } = string.Empty;
        public string BaseLoadService { get; set; } = string.Empty;
        public string HvacService { get; set; } = string.Empty;
        public string HotWaterService { get; set; } = string.Empty; // Hot Water service endpoint
        public string FoundationService { get; set; } = string.Empty; // Foundation service endpoint
        public string TemperatureService { get; set; } = string.Empty; // Temperature service endpoint
        public string AirInfiltrationService { get; set; } = string.Empty; // Air Infiltration service endpoint
        public string VentilationService { get; set; } = string.Empty; // Ventilation service endpoint
        public string GenerationService { get; set; } = string.Empty; // Generation service endpoint
        public string SimulationEngineService { get; set; } = string.Empty;
        public string UserService { get; set; } = string.Empty; // New user service endpoint
        public string ConsolidatedDataService { get; set; } = string.Empty; // New user service endpoint


        public Dictionary<string, string> GetServiceMap()
        {
            return new Dictionary<string, string>
            {
                { "houses", HouseFileService },
                { "weatherlibrary", WeatherService },
                { "envelope", EnvelopeService },
                { "baseload", BaseLoadService },
                { "heatingcooling", HvacService },
                { "hotwater", HotWaterService }, // Add hot water to service map
                { "foundation", FoundationService }, // Add foundation to service map
                { "temperature", TemperatureService }, // Add temperature to service map
                { "airinfiltration", AirInfiltrationService }, // Add air infiltration to service map
                { "ventilation", VentilationService }, // Add ventilation to service map
                { "generation", GenerationService }, // Add generation to service map
                { "simulations", SimulationEngineService },
                { "users", UserService },// Add users to service map,
                { "consolidateddata", ConsolidatedDataService } // Add users to service map
            };
        }
    }
}