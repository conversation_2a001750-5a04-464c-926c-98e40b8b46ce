// UPDATED Models/User.cs - Username + Password with full functionality
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hot2K.Gateway.Models
{
    [Table("Users", Schema = "users")]
    public class User
    {
        [Key]
        public Guid UserId { get; set; } = Guid.NewGuid();
        
        [Required]
        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string Password { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class CreateUserRequest
    {
        [Required]
        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string Password { get; set; } = string.Empty;
    }

    public class UpdateUserRequest
    {
        [Required]
        [MaxLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string Password { get; set; } = string.Empty;
    }

    public class UserResponse
    {
        public Guid UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        // NOTE: Password is NOT included in response for security
    }

    public class LoginRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    // New model for consolidated house data storage
    [Table("ConsolidatedHouseData", Schema = "gateway")]
    public class ConsolidatedHouseData
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid HouseId { get; set; }

        [Required]
        public int Version { get; set; }

        [Required]
        public string ConsolidatedDataJson { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    }

    // DTOs for consolidated data endpoints
    public class ConsolidatedHouseDataResponse
    {
        public Guid HouseId { get; set; }
        public HouseFileData? HouseFileData { get; set; }
        public WeatherData? WeatherData { get; set; }
        public EnvelopeData? EnvelopeData { get; set; }
        public BaseLoadData? BaseLoadData { get; set; }
        public HvacData? HvacData { get; set; }
        public HotWaterData? HotWaterData { get; set; }
        public FoundationData? FoundationData { get; set; }
        public TemperatureData? TemperatureData { get; set; }
        public AirInfiltrationData? AirInfiltrationData { get; set; }
        public VentilationData? VentilationData { get; set; }
        public GenerationData? GenerationData { get; set; }
        public SimulationData? SimulationData { get; set; }
        public DateTime RetrievedAt { get; set; } = DateTime.UtcNow;
    }

    public class SaveConsolidatedDataRequest
    {
        [Required]
        public Guid HouseId { get; set; }

        [Required]
        public ConsolidatedHouseDataResponse Data { get; set; } = new();
    }

    public class SaveConsolidatedDataResponse
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public int Version { get; set; }
        public DateTime SavedAt { get; set; }
    }

    // Data models for each microservice response
    public class HouseFileData
    {
        public object? HouseDetails { get; set; }
        public object? ProgramInformation { get; set; }
    }

    public class WeatherData
    {
        public object? WeatherLibrary { get; set; }
    }

    public class EnvelopeData
    {
        public object? Walls { get; set; }
        public object? Ceilings { get; set; }
        public object? Floors { get; set; }
        public object? FloorHeaders { get; set; }
        public object? Doors { get; set; }
        public object? Windows { get; set; }
        public object? ThermalPerformance { get; set; }
    }

    public class BaseLoadData
    {
        public object? BaseLoads { get; set; }
    }

    public class HvacData
    {
        public object? HeatingCooling { get; set; }
    }

    public class HotWaterData
    {
        public object? HotWater { get; set; }
    }

    public class FoundationData
    {
        public object? Foundation { get; set; }
    }

    public class TemperatureData
    {
        public object? Temperature { get; set; }
    }

    public class AirInfiltrationData
    {
        public object? AirInfiltration { get; set; }
    }

    public class VentilationData
    {
        public object? Ventilation { get; set; }
    }

    public class GenerationData
    {
        public object? Generation { get; set; }
    }

    public class SimulationData
    {
        public object? Results { get; set; }
    }
}