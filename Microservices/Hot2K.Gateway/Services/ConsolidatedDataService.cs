using Hot2K.Gateway.Configuration;
using Hot2K.Gateway.Data;
using Hot2K.Gateway.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace Hot2K.Gateway.Services
{
    public class ConsolidatedDataService : IConsolidatedDataService
    {
        private readonly HttpClient _httpClient;
        private readonly ServiceEndpoints _serviceEndpoints;
        private readonly UserDbContext _context;
        private readonly ILogger<ConsolidatedDataService> _logger;

        public ConsolidatedDataService(
            HttpClient httpClient,
            IOptions<ServiceEndpoints> serviceEndpoints,
            UserDbContext context,
            ILogger<ConsolidatedDataService> logger)
        {
            _httpClient = httpClient;
            _serviceEndpoints = serviceEndpoints.Value;
            _context = context;
            _logger = logger;
        }

        public async Task<ConsolidatedHouseDataResponse?> GetConsolidatedDataAsync(Guid houseId)
        {
            _logger.LogInformation("Getting consolidated data for house ID: {HouseId}", houseId);

            var response = new ConsolidatedHouseDataResponse
            {
                HouseId = houseId
            };

            try
            {
                // Fetch data from all microservices in parallel
                var tasks = new List<Task>
                {
                    FetchHouseFileDataAsync(houseId, response),
                    FetchWeatherDataAsync(houseId, response),
                    FetchEnvelopeDataAsync(houseId, response),
                    FetchBaseLoadDataAsync(houseId, response),
                    FetchHvacDataAsync(houseId, response),
                    FetchHotWaterDataAsync(houseId, response),
                    FetchFoundationDataAsync(houseId, response),
                    FetchTemperatureDataAsync(houseId, response),
                    FetchAirInfiltrationDataAsync(houseId, response),
                    FetchVentilationDataAsync(houseId, response),
                    FetchGenerationDataAsync(houseId, response),
                    FetchSimulationDataAsync(houseId, response)
                };

                await Task.WhenAll(tasks);

                _logger.LogInformation("Successfully retrieved consolidated data for house ID: {HouseId}", houseId);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving consolidated data for house ID: {HouseId}", houseId);
                throw;
            }
        }

        public async Task<SaveConsolidatedDataResponse> SaveConsolidatedDataAsync(SaveConsolidatedDataRequest request)
        {
            _logger.LogInformation("Saving consolidated data for house ID: {HouseId}", request.HouseId);

            try
            {
                // Get the next version number for this specific house ID
                // If house ID exists, increment version; if new house ID, start with version 1
                var latestVersion = await _context.ConsolidatedHouseData
                    .Where(x => x.HouseId == request.HouseId)
                    .MaxAsync(x => (int?)x.Version);

                var nextVersion = (latestVersion ?? 0) + 1;

                _logger.LogInformation("House ID: {HouseId} - Latest version: {LatestVersion}, Next version: {NextVersion}", 
                    request.HouseId, latestVersion, nextVersion);

                // Serialize the data to JSON
                var jsonData = JsonSerializer.Serialize(request.Data, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                });

                // Create new consolidated data record
                var consolidatedData = new ConsolidatedHouseData
                {
                    HouseId = request.HouseId,
                    Version = nextVersion,
                    ConsolidatedDataJson = jsonData,
                    CreatedAt = DateTime.UtcNow
                };

                _context.ConsolidatedHouseData.Add(consolidatedData);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Successfully saved consolidated data for house ID: {HouseId}, Version: {Version}", 
                    request.HouseId, nextVersion);

                return new SaveConsolidatedDataResponse
                {
                    Id = consolidatedData.Id,
                    HouseId = consolidatedData.HouseId,
                    Version = consolidatedData.Version,
                    SavedAt = consolidatedData.CreatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving consolidated data for house ID: {HouseId}", request.HouseId);
                throw;
            }
        }

        public async Task<ConsolidatedHouseData?> GetSavedConsolidatedDataAsync(Guid houseId, int? version = null)
        {
            _logger.LogInformation("Getting saved consolidated data for house ID: {HouseId}, Version: {Version}", 
                houseId, version);

            try
            {
                if (version.HasValue)
                {
                    return await _context.ConsolidatedHouseData
                        .FirstOrDefaultAsync(x => x.HouseId == houseId && x.Version == version.Value);
                }
                else
                {
                    // Get the latest version
                    return await _context.ConsolidatedHouseData
                        .Where(x => x.HouseId == houseId)
                        .OrderByDescending(x => x.Version)
                        .FirstOrDefaultAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting saved consolidated data for house ID: {HouseId}", houseId);
                throw;
            }
        }

        public async Task<IEnumerable<ConsolidatedHouseData>> GetAllVersionsAsync(Guid houseId)
        {
            _logger.LogInformation("Getting all versions for house ID: {HouseId}", houseId);

            try
            {
                return await _context.ConsolidatedHouseData
                    .Where(x => x.HouseId == houseId)
                    .OrderByDescending(x => x.Version)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all versions for house ID: {HouseId}", houseId);
                throw;
            }
        }

        private async Task FetchHouseFileDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching house file data for house ID: {HouseId}", houseId);
                
                // Fetch house details
                var houseResponse = await _httpClient.GetAsync($"{_serviceEndpoints.HouseFileService}/housefiles/{houseId}");
                if (houseResponse.IsSuccessStatusCode)
                {
                    var houseContent = await houseResponse.Content.ReadAsStringAsync();
                    var houseDetails = JsonSerializer.Deserialize<object>(houseContent);

                    // Fetch program information
                    var programResponse = await _httpClient.GetAsync($"{_serviceEndpoints.HouseFileService}/program-information/{houseId}");
                    object? programInfo = null;
                    if (programResponse.IsSuccessStatusCode)
                    {
                        var programContent = await programResponse.Content.ReadAsStringAsync();
                        programInfo = JsonSerializer.Deserialize<object>(programContent);
                    }

                    response.HouseFileData = new HouseFileData
                    {
                        HouseDetails = houseDetails,
                        ProgramInformation = programInfo
                    };
                    
                    _logger.LogDebug("Successfully fetched house file data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("House file service returned {StatusCode} for house ID: {HouseId}", 
                        houseResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch house file data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchWeatherDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching weather data for house ID: {HouseId}", houseId);
                
                var weatherResponse = await _httpClient.GetAsync($"{_serviceEndpoints.WeatherService}/house/{houseId}");
                if (weatherResponse.IsSuccessStatusCode)
                {
                    var weatherContent = await weatherResponse.Content.ReadAsStringAsync();
                    var weatherLibrary = JsonSerializer.Deserialize<object>(weatherContent);

                    response.WeatherData = new WeatherData
                    {
                        WeatherLibrary = weatherLibrary
                    };
                    
                    _logger.LogDebug("Successfully fetched weather data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Weather service returned {StatusCode} for house ID: {HouseId}", 
                        weatherResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch weather data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchEnvelopeDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching envelope data for house ID: {HouseId}", houseId);
                
                var tasks = new List<Task<(string key, object? data)>>
                {
                    FetchEnvelopeComponentAsync("walls", $"{_serviceEndpoints.EnvelopeService}/{houseId}/walls"),
                    FetchEnvelopeComponentAsync("ceilings", $"{_serviceEndpoints.EnvelopeService}/{houseId}/ceilings"),
                    FetchEnvelopeComponentAsync("floors", $"{_serviceEndpoints.EnvelopeService}/{houseId}/floors"),
                    FetchEnvelopeComponentAsync("floorheaders", $"{_serviceEndpoints.EnvelopeService}/{houseId}/floorheaders"),
                    FetchEnvelopeComponentAsync("doors", $"{_serviceEndpoints.EnvelopeService}/{houseId}/doors"),
                    FetchEnvelopeComponentAsync("windows", $"{_serviceEndpoints.EnvelopeService}/{houseId}/windows"),
                    FetchEnvelopeComponentAsync("thermal-performance", $"{_serviceEndpoints.EnvelopeService}/{houseId}/thermal-performance")
                };

                var results = await Task.WhenAll(tasks);
                
                response.EnvelopeData = new EnvelopeData
                {
                    Walls = results.FirstOrDefault(r => r.key == "walls").data,
                    Ceilings = results.FirstOrDefault(r => r.key == "ceilings").data,
                    Floors = results.FirstOrDefault(r => r.key == "floors").data,
                    FloorHeaders = results.FirstOrDefault(r => r.key == "floorheaders").data,
                    Doors = results.FirstOrDefault(r => r.key == "doors").data,
                    Windows = results.FirstOrDefault(r => r.key == "windows").data,
                    ThermalPerformance = results.FirstOrDefault(r => r.key == "thermal-performance").data
                };
                
                _logger.LogDebug("Successfully fetched envelope data for house ID: {HouseId}", houseId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch envelope data for house ID: {HouseId}", houseId);
            }
        }

        private async Task<(string key, object? data)> FetchEnvelopeComponentAsync(string key, string url)
        {
            try
            {
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<object>(content);
                    return (key, data);
                }
                else
                {
                    _logger.LogWarning("Envelope component {Key} service returned {StatusCode} from {Url}", 
                        key, response.StatusCode, url);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch envelope component {Key} from {Url}", key, url);
            }
            return (key, null);
        }

        private async Task FetchBaseLoadDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching base load data for house ID: {HouseId}", houseId);
                
                var baseLoadResponse = await _httpClient.GetAsync($"{_serviceEndpoints.BaseLoadService}/house/{houseId}");
                if (baseLoadResponse.IsSuccessStatusCode)
                {
                    var baseLoadContent = await baseLoadResponse.Content.ReadAsStringAsync();
                    var baseLoadData = JsonSerializer.Deserialize<object>(baseLoadContent);

                    response.BaseLoadData = new BaseLoadData
                    {
                        BaseLoads = baseLoadData
                    };
                    
                    _logger.LogDebug("Successfully fetched base load data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Base load service returned {StatusCode} for house ID: {HouseId}", 
                        baseLoadResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch base load data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchHvacDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching HVAC data for house ID: {HouseId}", houseId);

                var hvacResponse = await _httpClient.GetAsync($"{_serviceEndpoints.HvacService}/house/{houseId}");
                if (hvacResponse.IsSuccessStatusCode)
                {
                    var hvacContent = await hvacResponse.Content.ReadAsStringAsync();
                    var hvacData = JsonSerializer.Deserialize<object>(hvacContent);

                    response.HvacData = new HvacData
                    {
                        HeatingCooling = hvacData
                    };

                    _logger.LogDebug("Successfully fetched HVAC data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("HVAC service returned {StatusCode} for house ID: {HouseId}",
                        hvacResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch HVAC data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchHotWaterDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching hot water data for house ID: {HouseId}", houseId);

                var hotWaterResponse = await _httpClient.GetAsync($"{_serviceEndpoints.HotWaterService}/house/{houseId}");
                if (hotWaterResponse.IsSuccessStatusCode)
                {
                    var hotWaterContent = await hotWaterResponse.Content.ReadAsStringAsync();
                    var hotWaterData = JsonSerializer.Deserialize<object>(hotWaterContent);

                    response.HotWaterData = new HotWaterData
                    {
                        HotWater = hotWaterData
                    };

                    _logger.LogDebug("Successfully fetched hot water data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Hot water service returned {StatusCode} for house ID: {HouseId}",
                        hotWaterResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch hot water data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchFoundationDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching foundation data for house ID: {HouseId}", houseId);

                // Fetch all foundation types in parallel
                var basementTask = _httpClient.GetAsync($"{_serviceEndpoints.FoundationService}/{houseId}/basements");
                var crawlspaceTask = _httpClient.GetAsync($"{_serviceEndpoints.FoundationService}/{houseId}/crawlspaces");
                var slabTask = _httpClient.GetAsync($"{_serviceEndpoints.FoundationService}/{houseId}/slabs");
                var walkoutTask = _httpClient.GetAsync($"{_serviceEndpoints.FoundationService}/{houseId}/walkouts");

                await Task.WhenAll(basementTask, crawlspaceTask, slabTask, walkoutTask);

                var foundationData = new
                {
                    Basements = new List<object>(),
                    Crawlspaces = new List<object>(),
                    Slabs = new List<object>(),
                    Walkouts = new List<object>()
                };

                // Process basement data
                var basementResponse = await basementTask;
                if (basementResponse.IsSuccessStatusCode)
                {
                    var basementContent = await basementResponse.Content.ReadAsStringAsync();
                    var basementList = JsonSerializer.Deserialize<List<object>>(basementContent);
                    if (basementList != null)
                    {
                        foundationData.Basements.AddRange(basementList);
                    }
                }

                // Process crawlspace data
                var crawlspaceResponse = await crawlspaceTask;
                if (crawlspaceResponse.IsSuccessStatusCode)
                {
                    var crawlspaceContent = await crawlspaceResponse.Content.ReadAsStringAsync();
                    var crawlspaceList = JsonSerializer.Deserialize<List<object>>(crawlspaceContent);
                    if (crawlspaceList != null)
                    {
                        foundationData.Crawlspaces.AddRange(crawlspaceList);
                    }
                }

                // Process slab data
                var slabResponse = await slabTask;
                if (slabResponse.IsSuccessStatusCode)
                {
                    var slabContent = await slabResponse.Content.ReadAsStringAsync();
                    var slabList = JsonSerializer.Deserialize<List<object>>(slabContent);
                    if (slabList != null)
                    {
                        foundationData.Slabs.AddRange(slabList);
                    }
                }

                // Process walkout data
                var walkoutResponse = await walkoutTask;
                if (walkoutResponse.IsSuccessStatusCode)
                {
                    var walkoutContent = await walkoutResponse.Content.ReadAsStringAsync();
                    var walkoutList = JsonSerializer.Deserialize<List<object>>(walkoutContent);
                    if (walkoutList != null)
                    {
                        foundationData.Walkouts.AddRange(walkoutList);
                    }
                }

                response.FoundationData = new FoundationData
                {
                    Foundation = foundationData
                };

                _logger.LogDebug("Successfully fetched foundation data for house ID: {HouseId}", houseId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch foundation data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchTemperatureDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching temperature data for house ID: {HouseId}", houseId);

                var temperatureResponse = await _httpClient.GetAsync($"{_serviceEndpoints.TemperatureService}/house/{houseId}");
                if (temperatureResponse.IsSuccessStatusCode)
                {
                    var temperatureContent = await temperatureResponse.Content.ReadAsStringAsync();
                    var temperatureData = JsonSerializer.Deserialize<object>(temperatureContent);

                    response.TemperatureData = new TemperatureData
                    {
                        Temperature = temperatureData
                    };

                    _logger.LogDebug("Successfully fetched temperature data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Temperature service returned {StatusCode} for house ID: {HouseId}",
                        temperatureResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch temperature data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchAirInfiltrationDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching air infiltration data for house ID: {HouseId}", houseId);

                var airInfiltrationResponse = await _httpClient.GetAsync($"{_serviceEndpoints.AirInfiltrationService}/house/{houseId}");
                if (airInfiltrationResponse.IsSuccessStatusCode)
                {
                    var airInfiltrationContent = await airInfiltrationResponse.Content.ReadAsStringAsync();
                    var airInfiltrationData = JsonSerializer.Deserialize<object>(airInfiltrationContent);

                    response.AirInfiltrationData = new AirInfiltrationData
                    {
                        AirInfiltration = airInfiltrationData
                    };

                    _logger.LogDebug("Successfully fetched air infiltration data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Air infiltration service returned {StatusCode} for house ID: {HouseId}",
                        airInfiltrationResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch air infiltration data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchVentilationDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching ventilation data for house ID: {HouseId}", houseId);

                var ventilationResponse = await _httpClient.GetAsync($"{_serviceEndpoints.VentilationService}/house/{houseId}");
                if (ventilationResponse.IsSuccessStatusCode)
                {
                    var ventilationContent = await ventilationResponse.Content.ReadAsStringAsync();
                    var ventilationData = JsonSerializer.Deserialize<object>(ventilationContent);

                    response.VentilationData = new VentilationData
                    {
                        Ventilation = ventilationData
                    };

                    _logger.LogDebug("Successfully fetched ventilation data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Ventilation service returned {StatusCode} for house ID: {HouseId}",
                        ventilationResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch ventilation data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchGenerationDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching generation data for house ID: {HouseId}", houseId);

                var generationResponse = await _httpClient.GetAsync($"{_serviceEndpoints.GenerationService}/house/{houseId}");
                if (generationResponse.IsSuccessStatusCode)
                {
                    var generationContent = await generationResponse.Content.ReadAsStringAsync();
                    var generationData = JsonSerializer.Deserialize<object>(generationContent);

                    response.GenerationData = new GenerationData
                    {
                        Generation = generationData
                    };

                    _logger.LogDebug("Successfully fetched generation data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Generation service returned {StatusCode} for house ID: {HouseId}",
                        generationResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch generation data for house ID: {HouseId}", houseId);
            }
        }

        private async Task FetchSimulationDataAsync(Guid houseId, ConsolidatedHouseDataResponse response)
        {
            try
            {
                _logger.LogDebug("Fetching simulation data for house ID: {HouseId}", houseId);
                
                var simulationResponse = await _httpClient.GetAsync($"{_serviceEndpoints.SimulationEngineService}/results/house/{houseId}");
                if (simulationResponse.IsSuccessStatusCode)
                {
                    var simulationContent = await simulationResponse.Content.ReadAsStringAsync();
                    var simulationData = JsonSerializer.Deserialize<object>(simulationContent);

                    response.SimulationData = new SimulationData
                    {
                        Results = simulationData
                    };
                    
                    _logger.LogDebug("Successfully fetched simulation data for house ID: {HouseId}", houseId);
                }
                else
                {
                    _logger.LogWarning("Simulation service returned {StatusCode} for house ID: {HouseId}", 
                        simulationResponse.StatusCode, houseId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch simulation data for house ID: {HouseId}", houseId);
            }
        }
    }
}