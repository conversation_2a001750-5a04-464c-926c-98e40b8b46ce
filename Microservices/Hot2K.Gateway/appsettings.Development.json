{"ConnectionStrings": {"DefaultConnection": "Server=sql-server;Database=Hot2000Db;User=sa;Password=************;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "ServiceEndpoints": {"HouseFileService": "http://housefile-api:8080/api/houses", "WeatherService": "http://weather-api:8080/api/weatherlibrary", "EnvelopeService": "http://envelope-api:8080/api/envelope", "BaseLoadService": "http://baseload-api:8080/api/baseload", "HvacService": "http://hvac-api:8080/api/HeatingCooling", "HotWaterService": "http://hotwater-api:8080/api/HotWater", "FoundationService": "http://foundation-api:8080/api/foundation", "TemperatureService": "http://temperature-api:8080/api/temperature", "AirInfiltrationService": "http://airinfiltration-api:8080/api/airinfiltration", "VentilationService": "http://ventilation-api:8080/api/ventilation", "GenerationService": "http://generation-api:8080/api/generation", "SimulationEngineService": "http://simulation-api:8080/api/simulations"}}