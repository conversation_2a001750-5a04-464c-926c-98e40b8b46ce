using System;
using System.Collections.Generic;
using System.Text.Json;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Simplified data package for simulation input using JSON storage approach
    /// </summary>
    public class SimulationDataPackage
    {
        public Guid HouseId { get; set; }
        
        // Simulation Parameters
        public string SimulationType { get; set; }
        public string LanguageCode { get; set; } = string.Empty;
        public bool IsMetric { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TimeStepMinutes { get; set; }
        public double ConvergenceTolerance { get; set; }
        
        // Raw JSON Data - Much simpler approach like Hot2K Gateway
        public string HouseDataJson { get; set; }
        public string WeatherDataJson { get; set; }
        public string BaseloadDataJson { get; set; }
        public string WallsDataJson { get; set; }
        public string CeilingsDataJson { get; set; }
        public string FloorsDataJson { get; set; }
        public string FloorHeadersDataJson { get; set; }
        public string DoorsDataJson { get; set; }
        public string WindowsDataJson { get; set; }
        public string HvacData<PERSON>son { get; set; }
        public string HotWaterDataJson { get; set; }
        public string FoundationDataJson { get; set; }
        public string TemperatureDataJson { get; set; }
        public string AirInfiltrationDataJson { get; set; }
        public string GenerationDataJson { get; set; }

        // Metadata
        public DateTime CreatedAt { get; set; }
        
        // Helper methods to deserialize data when needed
        public HouseData GetHouseData()
        {
            return string.IsNullOrEmpty(HouseDataJson) 
                ? null 
                : JsonSerializer.Deserialize<HouseData>(HouseDataJson, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
        }
        
        public WeatherData GetWeatherData()
        {
            return string.IsNullOrEmpty(WeatherDataJson) 
                ? null 
                : JsonSerializer.Deserialize<WeatherData>(WeatherDataJson, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
        }
        
        public BaseloadData GetBaseloadData()
        {
            return string.IsNullOrEmpty(BaseloadDataJson) 
                ? null 
                : JsonSerializer.Deserialize<BaseloadData>(BaseloadDataJson, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
        }
        
        public List<EnvelopWallData> GetWallsData()
        {
            return string.IsNullOrEmpty(WallsDataJson) 
                ? new List<EnvelopWallData>() 
                : JsonSerializer.Deserialize<List<EnvelopWallData>>(WallsDataJson, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
        }
        
        public List<EnvelopCeilingData> GetCeilingsData()
        {
            return string.IsNullOrEmpty(CeilingsDataJson) 
                ? new List<EnvelopCeilingData>() 
                : JsonSerializer.Deserialize<List<EnvelopCeilingData>>(CeilingsDataJson, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
        }
        
        public List<EnvelopFloorData> GetFloorsData()
        {
            return string.IsNullOrEmpty(FloorsDataJson)
                ? new List<EnvelopFloorData>()
                : JsonSerializer.Deserialize<List<EnvelopFloorData>>(FloorsDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public List<EnvelopFloorHeaderData> GetFloorHeadersData()
        {
            return string.IsNullOrEmpty(FloorHeadersDataJson)
                ? new List<EnvelopFloorHeaderData>()
                : JsonSerializer.Deserialize<List<EnvelopFloorHeaderData>>(FloorHeadersDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public List<EnvelopDoorData> GetDoorsData()
        {
            return string.IsNullOrEmpty(DoorsDataJson)
                ? new List<EnvelopDoorData>()
                : JsonSerializer.Deserialize<List<EnvelopDoorData>>(DoorsDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public List<EnvelopWindowData> GetWindowsData()
        {
            return string.IsNullOrEmpty(WindowsDataJson)
                ? new List<EnvelopWindowData>()
                : JsonSerializer.Deserialize<List<EnvelopWindowData>>(WindowsDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public HvacData? GetHvacData()
        {
            return string.IsNullOrEmpty(HvacDataJson)
                ? null
                : JsonSerializer.Deserialize<HvacData>(HvacDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public HotWaterData? GetHotWaterData()
        {
            return string.IsNullOrEmpty(HotWaterDataJson)
                ? null
                : JsonSerializer.Deserialize<HotWaterData>(HotWaterDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public TemperatureData? GetTemperatureData()
        {
            return string.IsNullOrEmpty(TemperatureDataJson)
                ? null
                : JsonSerializer.Deserialize<TemperatureData>(TemperatureDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public AirInfiltrationData? GetAirInfiltrationData()
        {
            return string.IsNullOrEmpty(AirInfiltrationDataJson)
                ? null
                : JsonSerializer.Deserialize<AirInfiltrationData>(AirInfiltrationDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public FoundationData? GetFoundationData()
        {
            return string.IsNullOrEmpty(FoundationDataJson)
                ? null
                : JsonSerializer.Deserialize<FoundationData>(FoundationDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }

        public GenerationData? GetGenerationData()
        {
            return string.IsNullOrEmpty(GenerationDataJson)
                ? null
                : JsonSerializer.Deserialize<GenerationData>(GenerationDataJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }
}
