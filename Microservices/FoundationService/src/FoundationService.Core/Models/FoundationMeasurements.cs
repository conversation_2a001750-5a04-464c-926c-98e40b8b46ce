using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation measurements
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationMeasurements.cs
    /// </summary>
    public class FoundationMeasurements
    {
        public Guid Id { get; set; }

        public bool IsRectangular { get; set; } = false;
        public decimal Area { get; set; } = 0.0m;
        public decimal Width { get; set; } = 0.0m;
        public decimal Length { get; set; } = 0.0m;
        public decimal Perimeter { get; set; } = 0.0m;

        // Computed property from original
        public decimal FloorArea => IsRectangular ? Width * Length : Area;

        public FoundationMeasurements()
        {
            SetDefaults();
        }

        public FoundationMeasurements(FoundationMeasurements toCopy)
        {
            if (toCopy != null)
            {
                IsRectangular = toCopy.IsRectangular;
                Area = toCopy.Area;
                Width = toCopy.Width;
                Length = toCopy.Length;
                Perimeter = toCopy.Perimeter;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            IsRectangular = false;
            Area = 0.0m;
            Width = 0.0m;
            Length = 0.0m;
            Perimeter = 0.0m;
        }
    }
}