using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Code description and composite insulation
    /// Simplified version of Hot/HouseFileLibrary/CodeDescriptionAndComposite.cs
    /// </summary>
    public class CodeDescriptionAndComposite
    {
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [NotMapped]
        public List<RsiSection> Composite { get; set; } = new List<RsiSection>();

        // JSON string property for database persistence
        public string CompositeJson
        {
            get => JsonSerializer.Serialize(Composite);
            set => Composite = string.IsNullOrEmpty(value) ? new List<RsiSection>() : JsonSerializer.Deserialize<List<RsiSection>>(value) ?? new List<RsiSection>();
        }
        
        [StringLength(50)]
        public string IdRef { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;
        
        public decimal NominalInsulation { get; set; } = 0.0m;

        public CodeDescriptionAndComposite()
        {
        }

        public CodeDescriptionAndComposite(CodeReference toCopy)
        {
            if (toCopy != null)
            {
                IdRef = toCopy.IdRef;
                Code = toCopy.Code;
                NominalInsulation = toCopy.NominalInsulation;
                Description = toCopy.Text;
            }
        }

        public CodeDescriptionAndComposite(CodeDescriptionAndComposite toCopy)
        {
            if (toCopy != null)
            {
                IdRef = toCopy.IdRef;
                Code = toCopy.Code;
                NominalInsulation = toCopy.NominalInsulation;
                Description = toCopy.Description;
                Composite = new List<RsiSection>();
                foreach (var section in toCopy.Composite)
                {
                    Composite.Add(new RsiSection(section));
                }
            }
        }

        public decimal EffectiveRsiValue(decimal coreRsiValue = 0)
        {
            CalculateRemainder();

            // Ported from CFoundationData::RecalcRSI() in HOT2000
            decimal total = 0;
            foreach (var section in Composite)
                if (section.Percentage > 0 && (section.Rsi + coreRsiValue) != 0)
                    total += section.Percentage / (section.Rsi + coreRsiValue);

            decimal invTotal = (total == 0) ? 0 : 100 / total;
            return (invTotal < coreRsiValue) ? 0 : invTotal - coreRsiValue;
        }

        public void CalculateRemainder()
        {
            if (Composite.Count == 0) return;

            decimal remainder = 100m;
            foreach (var section in Composite)
                remainder -= section.Percentage;

            if (remainder > 0)
                Composite[Composite.Count - 1].Percentage = remainder;
        }

        public void SetDefaults()
        {
            Description = string.Empty;
            Composite = new List<RsiSection>();
            IdRef = string.Empty;
            Code = string.Empty;
            NominalInsulation = 0.0m;
        }
    }

    /// <summary>
    /// RSI section for composite insulation
    /// </summary>
    public class RsiSection
    {
        public decimal Percentage { get; set; } = 0.0m;
        public decimal Rsi { get; set; } = 0.0m;

        public RsiSection()
        {
        }

        public RsiSection(RsiSection toCopy)
        {
            if (toCopy != null)
            {
                Percentage = toCopy.Percentage;
                Rsi = toCopy.Rsi;
            }
        }
    }
}
