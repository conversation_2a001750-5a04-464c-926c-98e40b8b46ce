using System;
using System.Collections.Generic;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Ventilation types resource following H2kResources pattern
    /// Mirrors Hot/HouseFileLibrary/H2kResources/H2kResources.cs VentilationTypes
    /// </summary>
    public class VentilationTypes : ResourceList
    {
        public static readonly VentilationTypes Vented = new VentilationTypes("1", "Vented", "Ventilé");
        public static readonly VentilationTypes Open = new VentilationTypes("2", "Open", "Ouvert");
        public static readonly VentilationTypes Closed = new VentilationTypes("3", "Closed", "Fermé");

        public static List<VentilationTypes> All
        {
            get
            {
                List<VentilationTypes> allOptions = new List<VentilationTypes>();
                allOptions.Add(VentilationTypes.Vented);
                allOptions.Add(VentilationTypes.Open);
                allOptions.Add(VentilationTypes.Closed);
                return allOptions;
            }
        }

        // Private constructor
        private VentilationTypes(string code, string englishText, string frenchText, bool isUserSpecified = false)
            : base(code, englishText, frenchText, isUserSpecified) { }

        // This is just here to please the XSD schema generator and should NOT be used
        public VentilationTypes() { }

        public static VentilationTypes FromCode(string code)
        {
            foreach (var option in All)
            {
                if (option.Code == code)
                    return option;
            }
            return Closed; // Default fallback
        }

        public static VentilationTypes FromCodeAndText(CodeAndText codeAndText)
        {
            if (codeAndText == null)
                return Closed;
            
            return FromCode(codeAndText.Code);
        }
    }
}
