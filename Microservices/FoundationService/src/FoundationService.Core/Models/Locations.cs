using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Locations for walkout foundation calculations
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Locations.cs
    /// </summary>
    public class Locations
    {
        public Guid Id { get; set; }

        // Foreign key relationships for location coordinates
        public Guid L1_1Id { get; set; }
        public Location L1_1 { get; set; } = new Location();

        public Guid L1_2Id { get; set; }
        public Location L1_2 { get; set; } = new Location();

        public Guid L2_1Id { get; set; }
        public Location L2_1 { get; set; } = new Location();

        public Guid L2_2Id { get; set; }
        public Location L2_2 { get; set; } = new Location();

        public Locations()
        {
            SetDefaults();
        }

        public Locations(Locations toCopy)
        {
            if (toCopy != null)
            {
                L1_1 = toCopy.L1_1 != null ? new Location(toCopy.L1_1) : new Location();
                L1_2 = toCopy.L1_2 != null ? new Location(toCopy.L1_2) : new Location();
                L2_1 = toCopy.L2_1 != null ? new Location(toCopy.L2_1) : new Location();
                L2_2 = toCopy.L2_2 != null ? new Location(toCopy.L2_2) : new Location();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            L1_1 = new Location();
            L1_2 = new Location();
            L2_1 = new Location();
            L2_2 = new Location();
        }
    }
}