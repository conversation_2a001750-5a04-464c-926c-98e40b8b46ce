using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Crawlspace foundation type
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Crawlspace.cs
    /// </summary>
    public class Crawlspace : Foundation
    {
        // Resource property for XML serialization compatibility
        public VentilationTypes VentilationType { get; set; } = VentilationTypes.Closed;

        // Component properties with foreign keys
        public Guid FloorId { get; set; }
        public FoundationFloor Floor { get; set; } = new FoundationFloor();

        public Guid WallId { get; set; }
        public CrawlspaceWall Wall { get; set; } = new CrawlspaceWall();

        public Crawlspace() : base()
        {
            SetDefaults();
        }

        public Crawlspace(Crawlspace toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                VentilationType = toCopy.VentilationType;
                Floor = toCopy.Floor != null ? new FoundationFloor(toCopy.Floor) : new FoundationFloor();
                Wall = toCopy.Wall != null ? new CrawlspaceWall(toCopy.Wall) : new CrawlspaceWall();
            }
            else
            {
                SetDefaults();
            }
        }

        public override void SetDefaults()
        {
            base.SetDefaults();
            Label = "Crawlspace";
            VentilationType = VentilationTypes.Closed;
            Floor = new FoundationFloor();
            Wall = new CrawlspaceWall();
            Floor.SetDefaults();
            Wall.SetDefaults();
        }
    }
}