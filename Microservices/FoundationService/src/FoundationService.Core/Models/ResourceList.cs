using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Base class for resource list items following HvacService approach
    /// Reuses existing H2kResources.cs static values
    /// </summary>
    public class ResourceList
    {
        private string _code = string.Empty;
        private string _english = string.Empty;
        private string _french = string.Empty;

        public string Code
        {
            get => _code ?? string.Empty;
            set => _code = value ?? string.Empty;
        }

        public string English
        {
            get => _english ?? string.Empty;
            set => _english = value ?? string.Empty;
        }

        public string French
        {
            get => _french ?? string.Empty;
            set => _french = value ?? string.Empty;
        }

        public bool IsUserSpecified { get; set; } = false;

        public ResourceList()
        {
            SetDefaults();
        }

        public ResourceList(ResourceList toCopy)
        {
            Code = toCopy?.Code ?? string.Empty;
            English = toCopy?.English ?? string.Empty;
            French = toCopy?.French ?? string.Empty;
            IsUserSpecified = toCopy?.IsUserSpecified ?? false;
        }

        public ResourceList(string code, string englishText, string frenchText, bool isUserSpecified = false)
        {
            Code = code ?? string.Empty;
            English = englishText ?? string.Empty;
            French = frenchText ?? string.Empty;
            IsUserSpecified = isUserSpecified;
        }

        public virtual void SetDefaults()
        {
            _code = string.Empty;
            _english = string.Empty;
            _french = string.Empty;
            IsUserSpecified = false;
        }

        public virtual CodeAndText ToCodeAndText()
        {
            return new CodeAndText(Code, English, French);
        }

        // Operators
        public static implicit operator CodeAndText(ResourceList item)
        {
            return item?.ToCodeAndText() ?? new CodeAndText();
        }

        public static bool operator ==(ResourceList x, ResourceList y)
        {
            if (ReferenceEquals(x, null) && ReferenceEquals(y, null))
                return true;
            if (ReferenceEquals(x, null) || ReferenceEquals(y, null))
                return false;
            return x.Code == y.Code;
        }

        public static bool operator !=(ResourceList x, ResourceList y)
        {
            return !(x == y);
        }

        public override bool Equals(object? obj)
        {
            if (obj is ResourceList other)
                return this == other;
            return false;
        }

        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }

        public override string ToString()
        {
            return English;
        }
    }

    /// <summary>
    /// Resource list with numeric value support
    /// </summary>
    public class ResourceValueList : ResourceList
    {
        public decimal Value { get; set; } = 0.0m;

        public ResourceValueList() : base()
        {
        }

        public ResourceValueList(ResourceValueList toCopy) : base(toCopy)
        {
            Value = toCopy.Value;
        }

        public ResourceValueList(string code, string englishText, string frenchText, decimal value, bool isUserSpecified = false)
            : base(code, englishText, frenchText, isUserSpecified)
        {
            Value = value;
        }

        public override bool Equals(object? obj)
        {
            if (obj is ResourceValueList other)
            {
                return base.Equals(other) && Value == other.Value;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(base.GetHashCode(), Value);
        }

        public virtual CodeTextAndValue ToCodeTextAndValue()
        {
            return new CodeTextAndValue(Code, English, French, Value, IsUserSpecified);
        }

        // Operators
        public static implicit operator CodeTextAndValue(ResourceValueList item)
        {
            return item?.ToCodeTextAndValue() ?? new CodeTextAndValue();
        }
    }
}
