using System;
using System.Collections.Generic;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Openings upstairs resource following H2kResources pattern
    /// Mirrors Hot/HouseFileLibrary/H2kResources/H2kResources.cs OpeningsUpstairs
    /// </summary>
    public class OpeningsUpstairs : ResourceValueList
    {
        public static readonly OpeningsUpstairs StandardDoorOpen = new OpeningsUpstairs("1", "Standard door - open", "Porte standard - ouverte", 1.56m);
        public static readonly OpeningsUpstairs StandardDoorClosed = new OpeningsUpstairs("2", "Standard door - closed", "Porte standard - fermée", 0m);
        public static readonly OpeningsUpstairs Stairwell = new OpeningsUpstairs("3", "Stairwell", "Puits d'escalier", 8.64m);
        public static OpeningsUpstairs UserSpecified
        { get { return new OpeningsUpstairs("4", "User specified", "Spécifié par l'utilisateur", isUserSpecified: true); } }

        public static List<OpeningsUpstairs> All
        {
            get
            {
                List<OpeningsUpstairs> allOptions = new List<OpeningsUpstairs>();
                allOptions.Add(OpeningsUpstairs.StandardDoorOpen);
                allOptions.Add(OpeningsUpstairs.StandardDoorClosed);
                allOptions.Add(OpeningsUpstairs.Stairwell);
                allOptions.Add(OpeningsUpstairs.UserSpecified);
                return allOptions;
            }
        }

        // Private constructor
        private OpeningsUpstairs(string code, string englishText, string frenchText, decimal value = 0, bool isUserSpecified = false)
            : base(code, englishText, frenchText, value, isUserSpecified) { }

        // This is just here to please the XSD schema generator and should NOT be used
        public OpeningsUpstairs() { }

        public static OpeningsUpstairs FromCode(string code)
        {
            foreach (var option in All)
            {
                if (option.Code == code)
                    return option;
            }
            return StandardDoorOpen; // Default fallback
        }

        public static OpeningsUpstairs FromCodeAndText(CodeAndText codeAndText)
        {
            if (codeAndText == null)
                return StandardDoorOpen;
            
            return FromCode(codeAndText.Code);
        }

        public static OpeningsUpstairs FromCodeTextAndValue(CodeTextAndValue codeTextAndValue)
        {
            if (codeTextAndValue == null)
                return StandardDoorOpen;
            
            var result = FromCode(codeTextAndValue.Code);
            if (result.IsUserSpecified)
            {
                result.Value = codeTextAndValue.Value;
            }
            return result;
        }
    }
}
