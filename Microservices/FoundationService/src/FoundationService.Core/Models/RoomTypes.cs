using System;
using System.Collections.Generic;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Room types resource following H2kResources pattern
    /// Mirrors Hot/HouseFileLibrary/H2kResources/H2kResources.cs RoomTypes
    /// </summary>
    public class RoomTypes : ResourceList
    {
        public static readonly RoomTypes Kitchen = new RoomTypes("1", "Kitchen", "Cuisine");
        public static readonly RoomTypes LivingRoom = new RoomTypes("2", "Living Room", "Salon");
        public static readonly RoomTypes DiningRoom = new RoomTypes("3", "Dining Room", "Salle à Manger");
        public static readonly RoomTypes Bedroom = new RoomTypes("4", "Bedroom", "Chambre");
        public static readonly RoomTypes Bathroom = new RoomTypes("5", "Bathroom", "Salle de Bain");
        public static readonly RoomTypes UtilityRoom = new RoomTypes("6", "Utility Room", "Pièce Utilitaire");
        public static readonly RoomTypes Other = new RoomTypes("7", "Other", "Autre");

        public static List<RoomTypes> All
        {
            get
            {
                List<RoomTypes> allOptions = new List<RoomTypes>();
                allOptions.Add(RoomTypes.Kitchen);
                allOptions.Add(RoomTypes.LivingRoom);
                allOptions.Add(RoomTypes.DiningRoom);
                allOptions.Add(RoomTypes.Bedroom);
                allOptions.Add(RoomTypes.Bathroom);
                allOptions.Add(RoomTypes.UtilityRoom);
                allOptions.Add(RoomTypes.Other);
                return allOptions;
            }
        }

        // Private constructor
        private RoomTypes(string code, string englishText, string frenchText, bool isUserSpecified = false)
            : base(code, englishText, frenchText, isUserSpecified) { }

        // This is just here to please the XSD schema generator and should NOT be used
        public RoomTypes() { }

        public static RoomTypes FromCode(string code)
        {
            foreach (var option in All)
            {
                if (option.Code == code)
                    return option;
            }
            return UtilityRoom; // Default fallback
        }

        public static RoomTypes FromCodeAndText(CodeAndText codeAndText)
        {
            if (codeAndText == null)
                return UtilityRoom;
            
            return FromCode(codeAndText.Code);
        }
    }
}
