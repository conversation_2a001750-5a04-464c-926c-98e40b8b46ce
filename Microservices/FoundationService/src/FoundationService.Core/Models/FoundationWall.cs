using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation wall details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationWall.cs
    /// </summary>
    public class FoundationWall
    {
        public Guid Id { get; set; }

        public bool HasPonyWall { get; set; }

        // Foreign key relationships
        public Guid ConstructionId { get; set; }
        public FoundationWallConstruction Construction { get; set; } = new FoundationWallConstruction();

        public Guid MeasurementsId { get; set; }
        public FoundationWallMeasurements Measurements { get; set; } = new FoundationWallMeasurements();

        public FoundationWall()
        {
            SetDefaults();
        }

        public FoundationWall(FoundationWall toCopy)
        {
            HasPonyWall = toCopy.HasPonyWall;
            Construction = toCopy.Construction != null ? new FoundationWallConstruction(toCopy.Construction) : new FoundationWallConstruction();
            Measurements = toCopy.Measurements != null ? new FoundationWallMeasurements(toCopy.Measurements) : new FoundationWallMeasurements();
        }

        public void SetDefaults()
        {
            HasPonyWall = false;
            Construction = new FoundationWallConstruction();
            Measurements = new FoundationWallMeasurements();
            Construction.SetDefaults();
            Measurements.SetDefaults();
        }
    }
}