using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation configuration class
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Configuration.cs
    /// </summary>
    public class Configuration
    {
        public Guid Id { get; set; }

        [StringLength(50)]
        public string Type { get; set; } = string.Empty;
        
        public uint Subtype { get; set; } = 0;
        
        public decimal Overlap { get; set; } = 0.0m;
        
        public string Text
        {
            get { return string.Format("{0}_{1}", Type, Subtype); }
        }

        public Configuration()
        {
            SetDefaults();
        }

        public Configuration(Configuration toCopy)
        {
            if (toCopy != null)
            {
                Type = toCopy.Type;
                Subtype = toCopy.Subtype;
                Overlap = toCopy.Overlap;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Type = string.Empty;
            Subtype = 0;
            Overlap = 0.0m;
        }
    }
}
