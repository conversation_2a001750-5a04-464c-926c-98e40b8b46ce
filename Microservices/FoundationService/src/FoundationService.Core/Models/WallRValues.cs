using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Wall R-values for thermal calculations
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/WallRValues.cs
    /// </summary>
    public class WallRValues
    {
        public Guid Id { get; set; }

        public decimal Skirt { get; set; } = 0.0m;
        public decimal ThermalBreak { get; set; } = 0.0m;

        public WallRValues()
        {
            SetDefaults();
        }

        public WallRValues(WallRValues toCopy)
        {
            if (toCopy != null)
            {
                Skirt = toCopy.Skirt;
                ThermalBreak = toCopy.ThermalBreak;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Skirt = 0.0m;
            ThermalBreak = 0.0m;
        }
    }
}