using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Slab-on-grade foundation type
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Slab.cs
    /// </summary>
    public class Slab : Foundation
    {
        // Component properties with foreign keys
        public Guid FloorId { get; set; }
        public FoundationFloor Floor { get; set; } = new FoundationFloor();

        public Guid WallId { get; set; }
        public SlabWall Wall { get; set; } = new SlabWall();

        public Slab() : base()
        {
            SetDefaults();
        }

        public Slab(Slab toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                Floor = toCopy.Floor != null ? new FoundationFloor(toCopy.Floor) : new FoundationFloor();
                Wall = toCopy.Wall != null ? new SlabWall(toCopy.Wall) : new SlabWall();
            }
            else
            {
                SetDefaults();
            }
        }

        public override void SetDefaults()
        {
            base.SetDefaults();
            Label = "Slab-on-grade";
            Floor = new FoundationFloor();
            Wall = new SlabWall();
            Floor.SetDefaults();
            Wall.SetDefaults();
        }
    }
}