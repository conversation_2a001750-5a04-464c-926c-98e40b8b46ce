using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Exterior surfaces for walkout foundation calculations
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/ExteriorSurfaces.cs
    /// </summary>
    public class ExteriorSurfaces
    {
        public Guid Id { get; set; }

        public decimal AboveGradeArea { get; set; } = 0.0m;
        public decimal BelowGradeArea { get; set; } = 0.0m;
        public decimal PonyWallArea { get; set; } = 0.0m;
        public decimal SlabPerimeter { get; set; } = 0.0m;

        public ExteriorSurfaces()
        {
            SetDefaults();
        }

        public ExteriorSurfaces(ExteriorSurfaces toCopy)
        {
            if (toCopy != null)
            {
                AboveGradeArea = toCopy.AboveGradeArea;
                BelowGradeArea = toCopy.BelowGradeArea;
                PonyWallArea = toCopy.PonyWallArea;
                SlabPerimeter = toCopy.SlabPerimeter;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            AboveGradeArea = 0.0m;
            BelowGradeArea = 0.0m;
            PonyWallArea = 0.0m;
            SlabPerimeter = 0.0m;
        }
    }
}