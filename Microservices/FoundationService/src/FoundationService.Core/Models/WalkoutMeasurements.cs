using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Walkout foundation measurements
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/WalkoutMeasurements.cs
    /// </summary>
    public class WalkoutMeasurements
    {
        public Guid Id { get; set; }

        public bool WithSlab { get; set; } = false;
        public decimal Height { get; set; } = 0.0m;
        public decimal D1 { get; set; } = 0.0m;
        public decimal D2 { get; set; } = 0.0m;
        public decimal D3 { get; set; } = 0.0m;
        public decimal D4 { get; set; } = 0.0m;
        public decimal D5 { get; set; } = 0.0m;
        public decimal L1 { get; set; } = 0.0m;
        public decimal L2 { get; set; } = 0.0m;
        public decimal L3 { get; set; } = 0.0m;
        public decimal L4 { get; set; } = 0.0m;

        public WalkoutMeasurements()
        {
            SetDefaults();
        }

        public WalkoutMeasurements(WalkoutMeasurements toCopy)
        {
            if (toCopy != null)
            {
                WithSlab = toCopy.WithSlab;
                Height = toCopy.Height;
                D1 = toCopy.D1;
                D2 = toCopy.D2;
                D3 = toCopy.D3;
                D4 = toCopy.D4;
                D5 = toCopy.D5;
                L1 = toCopy.L1;
                L2 = toCopy.L2;
                L3 = toCopy.L3;
                L4 = toCopy.L4;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            WithSlab = false;
            Height = 0.0m;
            D1 = 0.0m;
            D2 = 0.0m;
            D3 = 0.0m;
            D4 = 0.0m;
            D5 = 0.0m;
            L1 = 0.0m;
            L2 = 0.0m;
            L3 = 0.0m;
            L4 = 0.0m;
        }
    }
}