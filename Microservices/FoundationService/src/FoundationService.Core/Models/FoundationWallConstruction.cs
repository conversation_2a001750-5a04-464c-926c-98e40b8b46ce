using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation wall construction details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationWallConstruction.cs
    /// </summary>
    public class FoundationWallConstruction
    {
        public Guid Id { get; set; }

        public ushort Corners { get; set; } = 0;
        public CodeDescriptionAndComposite InteriorAddedInsulation { get; set; } = new CodeDescriptionAndComposite();
        public CodeDescriptionAndComposite ExteriorAddedInsulation { get; set; } = new CodeDescriptionAndComposite();
        public CodeReference Lintels { get; set; } = new CodeReference();
        public CodeDescriptionAndComposite PonyWallType { get; set; } = new CodeDescriptionAndComposite();

        public FoundationWallConstruction()
        {
            SetDefaults();
        }

        public FoundationWallConstruction(FoundationWallConstruction toCopy)
        {
            if (toCopy != null)
            {
                Corners = toCopy.Corners;
                InteriorAddedInsulation = toCopy.InteriorAddedInsulation != null ? new CodeDescriptionAndComposite(toCopy.InteriorAddedInsulation) : new CodeDescriptionAndComposite();
                ExteriorAddedInsulation = toCopy.ExteriorAddedInsulation != null ? new CodeDescriptionAndComposite(toCopy.ExteriorAddedInsulation) : new CodeDescriptionAndComposite();
                Lintels = toCopy.Lintels != null ? new CodeReference(toCopy.Lintels) : new CodeReference();
                PonyWallType = toCopy.PonyWallType != null ? new CodeDescriptionAndComposite(toCopy.PonyWallType) : new CodeDescriptionAndComposite();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Corners = 0;
            InteriorAddedInsulation = new CodeDescriptionAndComposite();
            ExteriorAddedInsulation = new CodeDescriptionAndComposite();
            Lintels = new CodeReference();
            PonyWallType = new CodeDescriptionAndComposite();
        }
    }
}