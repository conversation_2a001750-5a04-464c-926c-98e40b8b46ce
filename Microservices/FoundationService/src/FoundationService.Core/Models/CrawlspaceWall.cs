using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Crawlspace wall details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/CrawlspaceWall.cs
    /// </summary>
    public class CrawlspaceWall
    {
        public Guid Id { get; set; }

        // Foreign key relationships
        // Note: Construction is optional, null for Open Crawlspaces
        public Guid? ConstructionId { get; set; }
        public CrawlspaceWallConstruction? Construction { get; set; } = new CrawlspaceWallConstruction();

        public Guid MeasurementsId { get; set; }
        public CrawlspaceWallMeasurements Measurements { get; set; } = new CrawlspaceWallMeasurements();

        public Guid RValuesId { get; set; }
        public WallRValues RValues { get; set; } = new WallRValues();

        public CrawlspaceWall()
        {
            SetDefaults();
        }

        public CrawlspaceWall(CrawlspaceWall toCopy)
        {
            if (toCopy != null)
            {
                Construction = toCopy.Construction != null ? new CrawlspaceWallConstruction(toCopy.Construction) : new CrawlspaceWallConstruction();
                Measurements = toCopy.Measurements != null ? new CrawlspaceWallMeasurements(toCopy.Measurements) : new CrawlspaceWallMeasurements();
                RValues = toCopy.RValues != null ? new WallRValues(toCopy.RValues) : new WallRValues();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Construction = new CrawlspaceWallConstruction();
            Measurements = new CrawlspaceWallMeasurements();
            RValues = new WallRValues();
            Construction.SetDefaults();
            Measurements.SetDefaults();
            RValues.SetDefaults();
        }
    }
}