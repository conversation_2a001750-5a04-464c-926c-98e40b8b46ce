using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Location coordinates for walkout foundation calculations
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Location.cs
    /// </summary>
    public class Location
    {
        public Guid Id { get; set; }

        public decimal X1 { get; set; } = 0.0m;
        public decimal X2 { get; set; } = 0.0m;

        public Location()
        {
            SetDefaults();
        }

        public Location(Location toCopy)
        {
            if (toCopy != null)
            {
                X1 = toCopy.X1;
                X2 = toCopy.X2;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            X1 = 0.0m;
            X2 = 0.0m;
        }
    }
}
