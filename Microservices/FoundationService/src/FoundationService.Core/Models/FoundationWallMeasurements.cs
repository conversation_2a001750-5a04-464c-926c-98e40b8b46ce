using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation wall measurements
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationWallMeasurements.cs
    /// </summary>
    public class FoundationWallMeasurements
    {
        public Guid Id { get; set; }

        public decimal Height { get; set; } = 0.0m;
        public decimal Depth { get; set; } = 0.0m;
        public decimal PonyWallHeight { get; set; } = 0.0m;

        public FoundationWallMeasurements()
        {
            SetDefaults();
        }

        public FoundationWallMeasurements(FoundationWallMeasurements toCopy)
        {
            if (toCopy != null)
            {
                Height = toCopy.Height;
                Depth = toCopy.Depth;
                PonyWallHeight = toCopy.PonyWallHeight;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Height = 0.0m;
            Depth = 0.0m;
            PonyWallHeight = 0.0m;
        }
    }
}