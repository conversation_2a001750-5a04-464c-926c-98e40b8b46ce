using System;

namespace FoundationService.Core.Models
{
    public class WalkoutFloor
    {
        public Guid Id { get; set; }

        public Guid ConstructionId { get; set; }
        public FoundationFloorConstruction Construction { get; set; } = new FoundationFloorConstruction();

        public WalkoutFloor()
        {
            SetDefaults();
        }

        public WalkoutFloor(WalkoutFloor toCopy)
        {
            Construction = toCopy.Construction != null ? new FoundationFloorConstruction(toCopy.Construction) : new FoundationFloorConstruction();
        }

        public void SetDefaults()
        {
            Construction = new FoundationFloorConstruction();
            Construction.SetDefaults();
        }
    }
} 