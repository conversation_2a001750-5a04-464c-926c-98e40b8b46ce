using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Code and text pair for resource items
    /// Following HvacService pattern
    /// </summary>
    public class CodeAndText
    {
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string English { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string French { get; set; } = string.Empty;

        public CodeAndText()
        {
        }

        public CodeAndText(string code, string english, string french)
        {
            Code = code ?? string.Empty;
            English = english ?? string.Empty;
            French = french ?? string.Empty;
        }

        public CodeAndText(CodeAndText toCopy)
        {
            if (toCopy != null)
            {
                Code = toCopy.Code;
                English = toCopy.English;
                French = toCopy.French;
            }
        }

        public override string ToString()
        {
            return English;
        }

        public override bool Equals(object? obj)
        {
            if (obj is CodeAndText other)
                return Code == other.Code;
            return false;
        }

        public override int GetHashCode()
        {
            return Code?.GetHashCode() ?? 0;
        }
    }

    /// <summary>
    /// Code, text and value for resource items with numeric values
    /// Following HvacService pattern
    /// </summary>
    public class CodeTextAndValue : CodeAndText
    {
        public decimal Value { get; set; } = 0.0m;
        public bool IsUserSpecified { get; set; } = false;

        public CodeTextAndValue() : base()
        {
        }

        public CodeTextAndValue(string code, string english, string french, decimal value = 0.0m, bool isUserSpecified = false)
            : base(code, english, french)
        {
            Value = value;
            IsUserSpecified = isUserSpecified;
        }

        public CodeTextAndValue(CodeTextAndValue toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                Value = toCopy.Value;
                IsUserSpecified = toCopy.IsUserSpecified;
            }
        }

        public override bool Equals(object? obj)
        {
            if (obj is CodeTextAndValue other)
                return base.Equals(other) && Value == other.Value;
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(base.GetHashCode(), Value);
        }
    }
}
