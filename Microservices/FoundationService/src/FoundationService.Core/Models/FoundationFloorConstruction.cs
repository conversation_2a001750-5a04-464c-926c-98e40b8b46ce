using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation floor construction details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationFloorConstruction.cs
    /// </summary>
    public class FoundationFloorConstruction
    {
        public Guid Id { get; set; }

        public bool IsBelowFrostline { get; set; } = false;
        public bool HasIntegralFooting { get; set; } = false;
        public bool HeatedFloor { get; set; } = false;
        public CodeReference AddedToSlab { get; set; } = new CodeReference();
        public CodeReference FloorsAbove { get; set; } = new CodeReference();

        public FoundationFloorConstruction()
        {
            SetDefaults();
        }

        public FoundationFloorConstruction(FoundationFloorConstruction toCopy)
        {
            if (toCopy != null)
            {
                IsBelowFrostline = toCopy.IsBelowFrostline;
                HasIntegralFooting = toCopy.HasIntegralFooting;
                HeatedFloor = toCopy.HeatedFloor;
                AddedToSlab = toCopy.AddedToSlab != null ? new CodeReference(toCopy.AddedToSlab) : new CodeReference();
                FloorsAbove = toCopy.FloorsAbove != null ? new CodeReference(toCopy.FloorsAbove) : new CodeReference();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            IsBelowFrostline = false;
            HasIntegralFooting = false;
            HeatedFloor = false;
            AddedToSlab = new CodeReference();
            FloorsAbove = new CodeReference();
        }
    }
}