using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Walkout foundation type
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Walkout.cs
    /// </summary>
    public class Walkout : Foundation
    {
        // Resource properties for XML serialization compatibility
        public OpeningsUpstairs OpeningUpstairs { get; set; } = OpeningsUpstairs.StandardDoorOpen;
        public RoomTypes RoomType { get; set; } = RoomTypes.UtilityRoom;

        // Component properties with foreign keys
        public Guid MeasurementsId { get; set; }
        public WalkoutMeasurements Measurements { get; set; } = new WalkoutMeasurements();

        public Guid FloorId { get; set; }
        public WalkoutFloor Floor { get; set; } = new WalkoutFloor();

        public Guid WallId { get; set; }
        public FoundationWall Wall { get; set; } = new FoundationWall();

        public Guid ExteriorSurfacesId { get; set; }
        public ExteriorSurfaces ExteriorSurfaces { get; set; } = new ExteriorSurfaces();

        public Guid LocationsId { get; set; }
        public Locations Locations { get; set; } = new Locations();

        public Walkout() : base()
        {
            SetDefaults();
        }

        public Walkout(Walkout toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                OpeningUpstairs = toCopy.OpeningUpstairs;
                RoomType = toCopy.RoomType;
                Measurements = toCopy.Measurements != null ? new WalkoutMeasurements(toCopy.Measurements) : new WalkoutMeasurements();
                Floor = toCopy.Floor != null ? new WalkoutFloor(toCopy.Floor) : new WalkoutFloor();
                Wall = toCopy.Wall != null ? new FoundationWall(toCopy.Wall) : new FoundationWall();
                ExteriorSurfaces = toCopy.ExteriorSurfaces != null ? new ExteriorSurfaces(toCopy.ExteriorSurfaces) : new ExteriorSurfaces();
                Locations = toCopy.Locations != null ? new Locations(toCopy.Locations) : new Locations();
            }
            else
            {
                SetDefaults();
            }
        }

        public override void SetDefaults()
        {
            base.SetDefaults();
            Label = "Walkout";
            OpeningUpstairs = OpeningsUpstairs.StandardDoorOpen;
            RoomType = RoomTypes.UtilityRoom;
            Measurements = new WalkoutMeasurements();
            Floor = new WalkoutFloor();
            Wall = new FoundationWall();
            ExteriorSurfaces = new ExteriorSurfaces();
            Locations = new Locations();

            Measurements.SetDefaults();
            Floor.SetDefaults();
            Wall.SetDefaults();
            ExteriorSurfaces.SetDefaults();
            Locations.SetDefaults();
        }
    }
}