namespace FoundationService.Core.Models
{
    /// <summary>
    /// Exposed surfaces for foundation calculations
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/ExposedSurfaces.cs
    /// </summary>
    public class ExposedSurfaces
    {
        public Guid Id { get; set; }

        public decimal ExteriorAboveGroundArea { get; set; } = 0.0m;
        public decimal ExteriorBelowGroundArea { get; set; } = 0.0m;
        public decimal InteriorAboveGroundArea { get; set; } = 0.0m;
        public decimal InteriorBelowGroundArea { get; set; } = 0.0m;
        public decimal PonyWallArea { get; set; } = 0.0m;
        public decimal WalkoutPerimeter { get; set; } = 0.0m;
        public decimal ExposedPerimeter { get; set; } = 0.0m;

        public ExposedSurfaces()
        {
            SetDefaults();
        }

        public ExposedSurfaces(ExposedSurfaces toCopy)
        {
            if (toCopy != null)
            {
                ExteriorAboveGroundArea = toCopy.ExteriorAboveGroundArea;
                ExteriorBelowGroundArea = toCopy.ExteriorBelowGroundArea;
                InteriorAboveGroundArea = toCopy.InteriorAboveGroundArea;
                InteriorBelowGroundArea = toCopy.InteriorBelowGroundArea;
                PonyWallArea = toCopy.PonyWallArea;
                WalkoutPerimeter = toCopy.WalkoutPerimeter;
                ExposedPerimeter = toCopy.ExposedPerimeter;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            ExteriorAboveGroundArea = 0.0m;
            ExteriorBelowGroundArea = 0.0m;
            InteriorAboveGroundArea = 0.0m;
            InteriorBelowGroundArea = 0.0m;
            PonyWallArea = 0.0m;
            WalkoutPerimeter = 0.0m;
            ExposedPerimeter = 0.0m;
        }
    }
}