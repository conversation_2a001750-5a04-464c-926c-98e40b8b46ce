using System;

namespace FoundationService.Core.Models
{
    public class SlabWall
    {
        public Guid Id { get; set; }

        public Guid RValuesId { get; set; }
        public WallRValues RValues { get; set; } = new WallRValues();

        public SlabWall()
        {
            SetDefaults();
        }

        public SlabWall(SlabWall toCopy)
        {
            RValues = toCopy.RValues != null ? new WallRValues(toCopy.RValues) : new WallRValues();
        }

        public void SetDefaults()
        {
            RValues = new WallRValues();
            RValues.SetDefaults();
        }
    }
} 