using System;
using System.ComponentModel.DataAnnotations;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Code reference for insulation and construction materials
    /// Simplified version of Hot/HouseFileLibrary/CodeReference.cs
    /// </summary>
    public class CodeReference
    {
        [StringLength(50)]
        public string IdRef { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;
        
        public decimal RValue { get; set; } = 0.0m;
        
        public decimal NominalInsulation { get; set; } = 0.0m;
        
        [StringLength(200)]
        public string Text { get; set; } = string.Empty;

        public CodeReference()
        {
        }

        public CodeReference(string code, string text, decimal rValue = 0m, decimal nominalInsulation = 0m)
        {
            Code = code ?? string.Empty;
            Text = text ?? string.Empty;
            RValue = rValue;
            NominalInsulation = nominalInsulation;
        }

        public CodeReference(CodeReference toCopy)
        {
            if (toCopy != null)
            {
                IdRef = toCopy.IdRef;
                Code = toCopy.Code;
                RValue = toCopy.RValue;
                NominalInsulation = toCopy.NominalInsulation;
                Text = toCopy.Text;
            }
        }

        public void SetDefaults()
        {
            IdRef = string.Empty;
            Code = string.Empty;
            RValue = 0.0m;
            NominalInsulation = 0.0m;
            Text = string.Empty;
        }
    }
}
