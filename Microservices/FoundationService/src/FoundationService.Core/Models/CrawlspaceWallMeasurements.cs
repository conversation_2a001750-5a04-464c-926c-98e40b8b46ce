using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Crawlspace wall measurements
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/CrawlspaceWallMeasurements.cs
    /// </summary>
    public class CrawlspaceWallMeasurements
    {
        public Guid Id { get; set; }

        public decimal Height { get; set; } = 0.0m;
        public decimal Depth { get; set; } = 0.0m;

        public CrawlspaceWallMeasurements()
        {
            SetDefaults();
        }

        public CrawlspaceWallMeasurements(CrawlspaceWallMeasurements toCopy)
        {
            if (toCopy != null)
            {
                Height = toCopy.Height;
                Depth = toCopy.Depth;
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Height = 0.0m;
            Depth = 0.0m;
        }
    }
}