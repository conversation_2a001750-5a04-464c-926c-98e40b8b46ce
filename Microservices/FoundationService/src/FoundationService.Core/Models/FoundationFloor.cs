using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Foundation floor details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/FoundationFloor.cs
    /// </summary>
    public class FoundationFloor
    {
        public Guid Id { get; set; }

        // Foreign key relationships
        public Guid ConstructionId { get; set; }

        public FoundationFloorConstruction Construction { get; set; } = new FoundationFloorConstruction();

        public Guid MeasurementsId { get; set; }
        public FoundationMeasurements Measurements { get; set; } = new FoundationMeasurements();

        public FoundationFloor()
        {
            SetDefaults();
        }

        public FoundationFloor(FoundationFloor toCopy)
        {
            Construction = toCopy.Construction != null ? new FoundationFloorConstruction(toCopy.Construction) : new FoundationFloorConstruction();
            Measurements = toCopy.Measurements != null ? new FoundationMeasurements(toCopy.Measurements) : new FoundationMeasurements();
        }

        public void SetDefaults()
        {
            Construction = new FoundationFloorConstruction();
            Measurements = new FoundationMeasurements();
            Construction.SetDefaults();
            Measurements.SetDefaults();
        }
    }
}