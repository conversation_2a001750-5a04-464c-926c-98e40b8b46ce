using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Basement foundation type
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/Basement.cs
    /// </summary>
    public class Basement : Foundation
    {
        // Resource properties for XML serialization compatibility
        public OpeningsUpstairs OpeningUpstairs { get; set; } = OpeningsUpstairs.StandardDoorOpen;
        public RoomTypes RoomType { get; set; } = RoomTypes.UtilityRoom;

        // Component properties with foreign keys
        public Guid FloorId { get; set; }
        public FoundationFloor Floor { get; set; } = new FoundationFloor();

        public Guid WallId { get; set; }
        public FoundationWall Wall { get; set; } = new FoundationWall();

        public Basement() : base()
        {
            SetDefaults();
        }

        public Basement(Basement toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                OpeningUpstairs = toCopy.OpeningUpstairs;
                RoomType = toCopy.RoomType;
                Floor = toCopy.Floor != null ? new FoundationFloor(toCopy.Floor) : new FoundationFloor();
                Wall = toCopy.Wall != null ? new FoundationWall(toCopy.Wall) : new FoundationWall();
            }
            else
            {
                SetDefaults();
            }
        }

        public override void SetDefaults()
        {
            base.SetDefaults();
            Label = "Basement";
            OpeningUpstairs = OpeningsUpstairs.StandardDoorOpen;
            RoomType = RoomTypes.UtilityRoom;
            Floor = new FoundationFloor();
            Wall = new FoundationWall();

            // Initialize measurements
            if (Wall.Measurements == null)
                Wall.Measurements = new FoundationWallMeasurements();

            Floor.SetDefaults();
            Wall.SetDefaults();
        }
    }
}