using System;

namespace FoundationService.Core.Models
{
    /// <summary>
    /// Crawlspace wall construction details
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/FoundationComponents/CrawlspaceWallConstruction.cs
    /// </summary>
    public class CrawlspaceWallConstruction
    {
        public Guid Id { get; set; }

        public ushort Corners { get; set; } = 0;
        public CodeDescriptionAndComposite Type { get; set; } = new CodeDescriptionAndComposite();
        public CodeReference Lintels { get; set; } = new CodeReference();

        public CrawlspaceWallConstruction()
        {
            SetDefaults();
        }

        public CrawlspaceWallConstruction(CrawlspaceWallConstruction toCopy)
        {
            if (toCopy != null)
            {
                Corners = toCopy.Corners;
                Type = toCopy.Type != null ? new CodeDescriptionAndComposite(toCopy.Type) : new CodeDescriptionAndComposite();
                Lintels = toCopy.Lintels != null ? new CodeReference(toCopy.Lintels) : new CodeReference();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Corners = 0;
            Type = new CodeDescriptionAndComposite();
            Lintels = new CodeReference();
        }
    }
}