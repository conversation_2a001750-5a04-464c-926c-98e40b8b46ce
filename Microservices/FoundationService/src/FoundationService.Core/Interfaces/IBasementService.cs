using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Models;

namespace FoundationService.Core.Interfaces
{
    /// <summary>
    /// Service interface for basement operations following EnvelopeService pattern
    /// </summary>
    public interface IBasementService
    {
        Task<IEnumerable<Basement>> GetBasementsByHouseIdAsync(Guid houseId);
        Task<Basement?> GetBasementByIdAsync(Guid id);
        Task<IEnumerable<Basement>> GetAllBasementsAsync();
        Task<Basement> AddBasementAsync(Basement basement);
        Task UpdateBasementAsync(Basement basement);
        Task DeleteBasementAsync(Guid id);
    }
}
