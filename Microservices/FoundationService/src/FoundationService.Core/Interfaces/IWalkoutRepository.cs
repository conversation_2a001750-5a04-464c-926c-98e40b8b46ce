using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Models;

namespace FoundationService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for walkout operations following EnvelopeService pattern
    /// </summary>
    public interface IWalkoutRepository
    {
        Task<IEnumerable<Walkout>> GetWalkoutsByHouseIdAsync(Guid houseId);
        Task<Walkout?> GetWalkoutByIdAsync(Guid id);
        Task<IEnumerable<Walkout>> GetAllWalkoutsAsync();
        Task<Walkout> AddWalkoutAsync(Walkout walkout);
        Task UpdateWalkoutAsync(Walkout walkout);
        Task DeleteWalkoutAsync(Guid id);
    }
}
