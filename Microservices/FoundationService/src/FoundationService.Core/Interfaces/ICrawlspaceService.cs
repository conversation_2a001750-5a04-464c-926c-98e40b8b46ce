using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Models;

namespace FoundationService.Core.Interfaces
{
    /// <summary>
    /// Service interface for crawlspace operations following EnvelopeService pattern
    /// </summary>
    public interface ICrawlspaceService
    {
        Task<IEnumerable<Crawlspace>> GetCrawlspacesByHouseIdAsync(Guid houseId);
        Task<Crawlspace?> GetCrawlspaceByIdAsync(Guid id);
        Task<IEnumerable<Crawlspace>> GetAllCrawlspacesAsync();
        Task<Crawlspace> AddCrawlspaceAsync(Crawlspace crawlspace);
        Task UpdateCrawlspaceAsync(Crawlspace crawlspace);
        Task DeleteCrawlspaceAsync(Guid id);
    }
}
