using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Models;

namespace FoundationService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for slab operations following EnvelopeService pattern
    /// </summary>
    public interface ISlabRepository
    {
        Task<IEnumerable<Slab>> GetSlabsByHouseIdAsync(Guid houseId);
        Task<Slab?> GetSlabByIdAsync(Guid id);
        Task<IEnumerable<Slab>> GetAllSlabsAsync();
        Task<Slab> AddSlabAsync(Slab slab);
        Task UpdateSlabAsync(Slab slab);
        Task DeleteSlabAsync(Guid id);
    }
}
