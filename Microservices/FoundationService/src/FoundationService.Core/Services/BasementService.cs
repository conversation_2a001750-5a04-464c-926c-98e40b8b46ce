using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;
using Microsoft.Extensions.Logging;

namespace FoundationService.Core.Services
{
    /// <summary>
    /// Service implementation for basement operations following EnvelopeService pattern
    /// </summary>
    public class BasementService : IBasementService
    {
        private readonly IBasementRepository _basementRepository;
        private readonly FoundationDbContext _dbContext;
        private readonly ILogger<BasementService> _logger;

        public BasementService(IBasementRepository basementRepository, FoundationDbContext dbContext, ILogger<BasementService> logger)
        {
            _basementRepository = basementRepository ?? throw new ArgumentNullException(nameof(basementRepository));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Basement>> GetBasementsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting basements for house ID: {HouseId}", houseId);
            return await _basementRepository.GetBasementsByHouseIdAsync(houseId);
        }

        public async Task<Basement?> GetBasementByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting basement by ID: {BasementId}", id);
            return await _basementRepository.GetBasementByIdAsync(id);
        }

        public async Task<IEnumerable<Basement>> GetAllBasementsAsync()
        {
            _logger.LogInformation("Getting all basements");
            return await _basementRepository.GetAllBasementsAsync();
        }

        public async Task<Basement> AddBasementAsync(Basement basement)
        {
            _logger.LogInformation("Adding new basement for house ID: {HouseId}", basement.HouseId);
            
            // Ensure proper initialization of nested objects
            if (basement.Floor == null)
                basement.Floor = new FoundationFloor();
            
            if (basement.Wall == null)
                basement.Wall = new FoundationWall();

            // Set up foreign key relationships
            if (basement.Floor.Id == Guid.Empty)
                basement.Floor.Id = Guid.NewGuid();
            basement.FloorId = basement.Floor.Id;

            if (basement.Wall.Id == Guid.Empty)
                basement.Wall.Id = Guid.NewGuid();
            basement.WallId = basement.Wall.Id;

            // Initialize nested construction and measurement objects
            if (basement.Floor.Construction == null)
                basement.Floor.Construction = new FoundationFloorConstruction();
            if (basement.Floor.Measurements == null)
                basement.Floor.Measurements = new FoundationMeasurements();
            if (basement.Wall.Construction == null)
                basement.Wall.Construction = new FoundationWallConstruction();
            if (basement.Wall.Measurements == null)
                basement.Wall.Measurements = new FoundationWallMeasurements();

            // Set up construction and measurement IDs
            if (basement.Floor.Construction.Id == Guid.Empty)
                basement.Floor.Construction.Id = Guid.NewGuid();
            basement.Floor.ConstructionId = basement.Floor.Construction.Id;

            if (basement.Floor.Measurements.Id == Guid.Empty)
                basement.Floor.Measurements.Id = Guid.NewGuid();
            basement.Floor.MeasurementsId = basement.Floor.Measurements.Id;

            if (basement.Wall.Construction.Id == Guid.Empty)
                basement.Wall.Construction.Id = Guid.NewGuid();
            basement.Wall.ConstructionId = basement.Wall.Construction.Id;

            if (basement.Wall.Measurements.Id == Guid.Empty)
                basement.Wall.Measurements.Id = Guid.NewGuid();
            basement.Wall.MeasurementsId = basement.Wall.Measurements.Id;

            // Save the basement first
            var savedBasement = await _basementRepository.AddBasementAsync(basement);

            // Calculate and save ExposedSurfaces if this is an exposed surface
            if (savedBasement.IsExposedSurface && savedBasement.ExposedSurfacePerimeter > 0)
            {
                var exposedSurfaces = savedBasement.GetExtFndPortions();

                // Set a unique ID for the ExposedSurfaces record
                exposedSurfaces.Id = Guid.NewGuid();

                // Save the calculated ExposedSurfaces to the database
                _dbContext.ExposedSurfaces.Add(exposedSurfaces);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Calculated and saved ExposedSurfaces for basement {BasementId} with ExposedSurfaces ID {ExposedSurfacesId}: " +
                    "ExteriorAboveGround={ExteriorAboveGround}, ExteriorBelowGround={ExteriorBelowGround}, " +
                    "PonyWall={PonyWall}, ExposedPerimeter={ExposedPerimeter}",
                    savedBasement.Id,
                    exposedSurfaces.Id,
                    exposedSurfaces.ExteriorAboveGroundArea,
                    exposedSurfaces.ExteriorBelowGroundArea,
                    exposedSurfaces.PonyWallArea,
                    exposedSurfaces.ExposedPerimeter);
            }

            return savedBasement;
        }

        public async Task UpdateBasementAsync(Basement basement)
        {
            _logger.LogInformation("Updating basement with ID: {BasementId}", basement.Id);
            
            // Ensure foreign key relationships are maintained
            if (basement.Floor != null)
            {
                basement.FloorId = basement.Floor.Id;
                if (basement.Floor.Construction != null)
                    basement.Floor.ConstructionId = basement.Floor.Construction.Id;
                if (basement.Floor.Measurements != null)
                    basement.Floor.MeasurementsId = basement.Floor.Measurements.Id;
            }

            if (basement.Wall != null)
            {
                basement.WallId = basement.Wall.Id;
                if (basement.Wall.Construction != null)
                    basement.Wall.ConstructionId = basement.Wall.Construction.Id;
                if (basement.Wall.Measurements != null)
                    basement.Wall.MeasurementsId = basement.Wall.Measurements.Id;
            }

            await _basementRepository.UpdateBasementAsync(basement);
        }

        public async Task DeleteBasementAsync(Guid id)
        {
            _logger.LogInformation("Deleting basement with ID: {BasementId}", id);
            await _basementRepository.DeleteBasementAsync(id);
        }
    }
}
