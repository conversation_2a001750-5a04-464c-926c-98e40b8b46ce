using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using Microsoft.Extensions.Logging;

namespace FoundationService.Core.Services
{
    /// <summary>
    /// Service implementation for crawlspace operations following EnvelopeService pattern
    /// </summary>
    public class CrawlspaceService : ICrawlspaceService
    {
        private readonly ICrawlspaceRepository _crawlspaceRepository;
        private readonly ILogger<CrawlspaceService> _logger;

        public CrawlspaceService(ICrawlspaceRepository crawlspaceRepository, ILogger<CrawlspaceService> logger)
        {
            _crawlspaceRepository = crawlspaceRepository ?? throw new ArgumentNullException(nameof(crawlspaceRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Crawlspace>> GetCrawlspacesByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting crawlspaces for house ID: {HouseId}", houseId);
            return await _crawlspaceRepository.GetCrawlspacesByHouseIdAsync(houseId);
        }

        public async Task<Crawlspace?> GetCrawlspaceByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting crawlspace by ID: {CrawlspaceId}", id);
            return await _crawlspaceRepository.GetCrawlspaceByIdAsync(id);
        }

        public async Task<IEnumerable<Crawlspace>> GetAllCrawlspacesAsync()
        {
            _logger.LogInformation("Getting all crawlspaces");
            return await _crawlspaceRepository.GetAllCrawlspacesAsync();
        }

        public async Task<Crawlspace> AddCrawlspaceAsync(Crawlspace crawlspace)
        {
            _logger.LogInformation("Adding new crawlspace for house ID: {HouseId}", crawlspace.HouseId);
            
            // Ensure proper initialization of nested objects
            if (crawlspace.Floor == null)
                crawlspace.Floor = new FoundationFloor();
            
            if (crawlspace.Wall == null)
                crawlspace.Wall = new CrawlspaceWall();

            // Set up foreign key relationships
            if (crawlspace.Floor.Id == Guid.Empty)
                crawlspace.Floor.Id = Guid.NewGuid();
            crawlspace.FloorId = crawlspace.Floor.Id;

            if (crawlspace.Wall.Id == Guid.Empty)
                crawlspace.Wall.Id = Guid.NewGuid();
            crawlspace.WallId = crawlspace.Wall.Id;

            // Initialize nested objects for floor
            if (crawlspace.Floor.Construction == null)
                crawlspace.Floor.Construction = new FoundationFloorConstruction();
            if (crawlspace.Floor.Measurements == null)
                crawlspace.Floor.Measurements = new FoundationMeasurements();

            // Initialize nested objects for wall (construction is optional for open crawlspaces)
            if (crawlspace.Wall.Measurements == null)
                crawlspace.Wall.Measurements = new CrawlspaceWallMeasurements();
            if (crawlspace.Wall.RValues == null)
                crawlspace.Wall.RValues = new WallRValues();

            // Set up floor IDs
            if (crawlspace.Floor.Construction.Id == Guid.Empty)
                crawlspace.Floor.Construction.Id = Guid.NewGuid();
            crawlspace.Floor.ConstructionId = crawlspace.Floor.Construction.Id;

            if (crawlspace.Floor.Measurements.Id == Guid.Empty)
                crawlspace.Floor.Measurements.Id = Guid.NewGuid();
            crawlspace.Floor.MeasurementsId = crawlspace.Floor.Measurements.Id;

            // Set up wall IDs
            if (crawlspace.Wall.Construction != null)
            {
                if (crawlspace.Wall.Construction.Id == Guid.Empty)
                    crawlspace.Wall.Construction.Id = Guid.NewGuid();
                crawlspace.Wall.ConstructionId = crawlspace.Wall.Construction.Id;
            }

            if (crawlspace.Wall.Measurements.Id == Guid.Empty)
                crawlspace.Wall.Measurements.Id = Guid.NewGuid();
            crawlspace.Wall.MeasurementsId = crawlspace.Wall.Measurements.Id;

            if (crawlspace.Wall.RValues.Id == Guid.Empty)
                crawlspace.Wall.RValues.Id = Guid.NewGuid();
            crawlspace.Wall.RValuesId = crawlspace.Wall.RValues.Id;

            return await _crawlspaceRepository.AddCrawlspaceAsync(crawlspace);
        }

        public async Task UpdateCrawlspaceAsync(Crawlspace crawlspace)
        {
            _logger.LogInformation("Updating crawlspace with ID: {CrawlspaceId}", crawlspace.Id);
            
            // Ensure foreign key relationships are maintained
            if (crawlspace.Floor != null)
            {
                crawlspace.FloorId = crawlspace.Floor.Id;
                if (crawlspace.Floor.Construction != null)
                    crawlspace.Floor.ConstructionId = crawlspace.Floor.Construction.Id;
                if (crawlspace.Floor.Measurements != null)
                    crawlspace.Floor.MeasurementsId = crawlspace.Floor.Measurements.Id;
            }

            if (crawlspace.Wall != null)
            {
                crawlspace.WallId = crawlspace.Wall.Id;
                if (crawlspace.Wall.Construction != null)
                    crawlspace.Wall.ConstructionId = crawlspace.Wall.Construction.Id;
                if (crawlspace.Wall.Measurements != null)
                    crawlspace.Wall.MeasurementsId = crawlspace.Wall.Measurements.Id;
                if (crawlspace.Wall.RValues != null)
                    crawlspace.Wall.RValuesId = crawlspace.Wall.RValues.Id;
            }

            await _crawlspaceRepository.UpdateCrawlspaceAsync(crawlspace);
        }

        public async Task DeleteCrawlspaceAsync(Guid id)
        {
            _logger.LogInformation("Deleting crawlspace with ID: {CrawlspaceId}", id);
            await _crawlspaceRepository.DeleteCrawlspaceAsync(id);
        }
    }
}
