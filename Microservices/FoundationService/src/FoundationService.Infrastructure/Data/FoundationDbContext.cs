using Microsoft.EntityFrameworkCore;
using FoundationService.Core.Models;

namespace FoundationService.Infrastructure.Data
{
    public class FoundationDbContext : DbContext
    {
        public FoundationDbContext(DbContextOptions<FoundationDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.ForeignKeyPropertiesMappedToUnrelatedTables));
        }
        
        // Main foundation entities
        public DbSet<Foundation> Foundations { get; set; } = null!;
        public DbSet<Basement> Basements { get; set; } = null!;
        public DbSet<Crawlspace> Crawlspaces { get; set; } = null!;
        public DbSet<Slab> Slabs { get; set; } = null!;
        public DbSet<Walkout> Walkouts { get; set; } = null!;

        // Foundation component entities
        public DbSet<FoundationFloor> FoundationFloors { get; set; } = null!;
        public DbSet<FoundationWall> FoundationWalls { get; set; } = null!;
        public DbSet<CrawlspaceWall> CrawlspaceWalls { get; set; } = null!;
        public DbSet<SlabWall> SlabWalls { get; set; } = null!;
        public DbSet<WalkoutFloor> WalkoutFloors { get; set; } = null!;

        // Construction and measurement entities
        public DbSet<FoundationFloorConstruction> FoundationFloorConstructions { get; set; } = null!;
        public DbSet<FoundationWallConstruction> FoundationWallConstructions { get; set; } = null!;
        public DbSet<FoundationWallMeasurements> FoundationWallMeasurements { get; set; } = null!;
        public DbSet<FoundationMeasurements> FoundationMeasurements { get; set; } = null!;
        public DbSet<CrawlspaceWallConstruction> CrawlspaceWallConstructions { get; set; } = null!;
        public DbSet<CrawlspaceWallMeasurements> CrawlspaceWallMeasurements { get; set; } = null!;
        public DbSet<WallRValues> WallRValues { get; set; } = null!;

        // Walkout-specific entities
        public DbSet<WalkoutMeasurements> WalkoutMeasurements { get; set; } = null!;
        public DbSet<ExteriorSurfaces> ExteriorSurfaces { get; set; } = null!;
        public DbSet<ExposedSurfaces> ExposedSurfaces { get; set; } = null!;
        public DbSet<Locations> Locations { get; set; } = null!;
        public DbSet<Location> LocationCoordinates { get; set; } = null!;

        // Configuration entities
        public DbSet<Configuration> Configurations { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("foundation");

            // Ignore resource classes - they are not database entities
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeDescriptionAndComposite>();

            base.OnModelCreating(modelBuilder);

            // Ignore resource classes - they are not database entities
            modelBuilder.Ignore<ResourceList>();
            modelBuilder.Ignore<ResourceValueList>();
            modelBuilder.Ignore<OpeningsUpstairs>();
            modelBuilder.Ignore<RoomTypes>();
            modelBuilder.Ignore<VentilationTypes>();

            // Ignore base classes that are used for composition, not inheritance
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeTextAndValue>();
            modelBuilder.Ignore<CodeReference>();
            // Note: CodeDescriptionAndComposite is configured as owned entity, not ignored
            modelBuilder.Ignore<RsiSection>();

            base.OnModelCreating(modelBuilder);

            // ===============================
            // FOUNDATION BASE CLASS CONFIGURATION
            // ===============================

            modelBuilder.Entity<Foundation>(entity =>
            {
                entity.ToTable("Foundations", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).HasMaxLength(100);
                entity.Property(e => e.IsExposedSurface);
                entity.Property(e => e.ExposedSurfacePerimeter).HasPrecision(18, 2);

                // One-to-one relationship with Configuration
                entity.HasOne(e => e.Configuration)
                      .WithOne()
                      .HasForeignKey<Configuration>(c => c.Id)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure inheritance using Table Per Type (TPT)
                entity.UseTptMappingStrategy();
            });

            // ===============================
            // FOUNDATION TYPE CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<Basement>(entity =>
            {
                entity.ToTable("Basements", "foundation");

                // One-to-one relationships with components
                entity.HasOne(e => e.Floor)
                      .WithOne()
                      .HasForeignKey<Basement>(b => b.FloorId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wall)
                      .WithOne()
                      .HasForeignKey<Basement>(b => b.WallId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure resource properties as owned entities
                entity.OwnsOne(e => e.OpeningUpstairs, ou =>
                {
                    ou.Property(o => o.Code).HasMaxLength(10);
                    ou.Property(o => o.English).HasMaxLength(100);
                    ou.Property(o => o.French).HasMaxLength(100);
                    ou.Property(o => o.Value).HasPrecision(18, 2);
                    ou.Property(o => o.IsUserSpecified);
                });

                entity.OwnsOne(e => e.RoomType, rt =>
                {
                    rt.Property(r => r.Code).HasMaxLength(10);
                    rt.Property(r => r.English).HasMaxLength(100);
                    rt.Property(r => r.French).HasMaxLength(100);
                    rt.Property(r => r.IsUserSpecified);
                });
            });

            modelBuilder.Entity<Crawlspace>(entity =>
            {
                entity.ToTable("Crawlspaces", "foundation");

                // One-to-one relationships with components
                entity.HasOne(e => e.Floor)
                      .WithOne()
                      .HasForeignKey<Crawlspace>(c => c.FloorId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wall)
                      .WithOne()
                      .HasForeignKey<Crawlspace>(c => c.WallId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure resource properties as owned entities
                entity.OwnsOne(e => e.VentilationType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });
            });

            modelBuilder.Entity<Slab>(entity =>
            {
                entity.ToTable("Slabs", "foundation");

                // One-to-one relationships with components
                entity.HasOne(e => e.Floor)
                      .WithOne()
                      .HasForeignKey<Slab>(s => s.FloorId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wall)
                      .WithOne()
                      .HasForeignKey<Slab>(s => s.WallId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<Walkout>(entity =>
            {
                entity.ToTable("Walkouts", "foundation");

                // One-to-one relationships with components
                entity.HasOne(e => e.Measurements)
                      .WithOne()
                      .HasForeignKey<Walkout>(w => w.MeasurementsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Floor)
                      .WithOne()
                      .HasForeignKey<Walkout>(w => w.FloorId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wall)
                      .WithOne()
                      .HasForeignKey<Walkout>(w => w.WallId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ExteriorSurfaces)
                      .WithOne()
                      .HasForeignKey<Walkout>(w => w.ExteriorSurfacesId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Locations)
                      .WithOne()
                      .HasForeignKey<Walkout>(w => w.LocationsId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure resource properties as owned entities
                entity.OwnsOne(e => e.OpeningUpstairs, ou =>
                {
                    ou.Property(o => o.Code).HasMaxLength(10);
                    ou.Property(o => o.English).HasMaxLength(100);
                    ou.Property(o => o.French).HasMaxLength(100);
                    ou.Property(o => o.Value).HasPrecision(18, 2);
                    ou.Property(o => o.IsUserSpecified);
                });

                entity.OwnsOne(e => e.RoomType, rt =>
                {
                    rt.Property(r => r.Code).HasMaxLength(10);
                    rt.Property(r => r.English).HasMaxLength(100);
                    rt.Property(r => r.French).HasMaxLength(100);
                    rt.Property(r => r.IsUserSpecified);
                });
            });

            // ===============================
            // FOUNDATION COMPONENT CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<FoundationFloor>(entity =>
            {
                entity.ToTable("FoundationFloors", "foundation");
                entity.HasKey(e => e.Id);

                // One-to-one relationships with sub-components
                entity.HasOne(e => e.Construction)
                      .WithOne()
                      .HasForeignKey<FoundationFloor>(f => f.ConstructionId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                      .WithOne()
                      .HasForeignKey<FoundationFloor>(f => f.MeasurementsId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<FoundationWall>(entity =>
            {
                entity.ToTable("FoundationWalls", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HasPonyWall);

                // One-to-one relationships with sub-components
                entity.HasOne(e => e.Construction)
                      .WithOne()
                      .HasForeignKey<FoundationWall>(w => w.ConstructionId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                      .WithOne()
                      .HasForeignKey<FoundationWall>(w => w.MeasurementsId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<CrawlspaceWall>(entity =>
            {
                entity.ToTable("CrawlspaceWalls", "foundation");
                entity.HasKey(e => e.Id);

                // Optional one-to-one relationship with construction (null for Open Crawlspaces)
                entity.HasOne(e => e.Construction)
                      .WithOne()
                      .HasForeignKey<CrawlspaceWall>(cw => cw.ConstructionId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                      .WithOne()
                      .HasForeignKey<CrawlspaceWall>(cw => cw.MeasurementsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.RValues)
                      .WithOne()
                      .HasForeignKey<CrawlspaceWall>(cw => cw.RValuesId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SlabWall>(entity =>
            {
                entity.ToTable("SlabWalls", "foundation");
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.RValues)
                      .WithOne()
                      .HasForeignKey<SlabWall>(sw => sw.RValuesId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<WalkoutFloor>(entity =>
            {
                entity.ToTable("WalkoutFloors", "foundation");
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.Construction)
                      .WithOne()
                      .HasForeignKey<WalkoutFloor>(wf => wf.ConstructionId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // CONSTRUCTION AND MEASUREMENT CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<FoundationFloorConstruction>(entity =>
            {
                entity.ToTable("FoundationFloorConstructions", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.IsBelowFrostline);
                entity.Property(e => e.HasIntegralFooting);
                entity.Property(e => e.HeatedFloor);

                // Configure CodeReference properties as owned entities
                entity.OwnsOne(e => e.AddedToSlab, ats =>
                {
                    ats.Property(a => a.IdRef).HasMaxLength(50);
                    ats.Property(a => a.Code).HasMaxLength(50);
                    ats.Property(a => a.RValue).HasPrecision(18, 2);
                    ats.Property(a => a.NominalInsulation).HasPrecision(18, 2);
                    ats.Property(a => a.Text).HasMaxLength(200);
                });

                entity.OwnsOne(e => e.FloorsAbove, fa =>
                {
                    fa.Property(f => f.IdRef).HasMaxLength(50);
                    fa.Property(f => f.Code).HasMaxLength(50);
                    fa.Property(f => f.RValue).HasPrecision(18, 2);
                    fa.Property(f => f.NominalInsulation).HasPrecision(18, 2);
                    fa.Property(f => f.Text).HasMaxLength(200);
                });
            });

            modelBuilder.Entity<FoundationWallConstruction>(entity =>
            {
                entity.ToTable("FoundationWallConstructions", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Corners);

                // Configure InteriorAddedInsulation as owned entity
                entity.OwnsOne(e => e.InteriorAddedInsulation, iai =>
                {
                    iai.Property(cd => cd.Code).HasMaxLength(50);
                    iai.Property(cd => cd.Description).HasMaxLength(200);
                    iai.Property(cd => cd.IdRef).HasMaxLength(50);
                    iai.Property(cd => cd.NominalInsulation).HasPrecision(18, 2);
                    iai.Property(cd => cd.CompositeJson).HasMaxLength(2000);
                    iai.Ignore(cd => cd.Composite); // Ignore the List<RsiSection> property
                });

                // Configure ExteriorAddedInsulation as owned entity
                entity.OwnsOne(e => e.ExteriorAddedInsulation, eai =>
                {
                    eai.Property(cd => cd.Code).HasMaxLength(50);
                    eai.Property(cd => cd.Description).HasMaxLength(200);
                    eai.Property(cd => cd.IdRef).HasMaxLength(50);
                    eai.Property(cd => cd.NominalInsulation).HasPrecision(18, 2);
                    eai.Property(cd => cd.CompositeJson).HasMaxLength(2000);
                    eai.Ignore(cd => cd.Composite); // Ignore the List<RsiSection> property
                });

                // Configure PonyWallType as owned entity
                entity.OwnsOne(e => e.PonyWallType, pwt =>
                {
                    pwt.Property(cd => cd.Code).HasMaxLength(50);
                    pwt.Property(cd => cd.Description).HasMaxLength(200);
                    pwt.Property(cd => cd.IdRef).HasMaxLength(50);
                    pwt.Property(cd => cd.NominalInsulation).HasPrecision(18, 2);
                    pwt.Property(cd => cd.CompositeJson).HasMaxLength(2000);
                    pwt.Ignore(cd => cd.Composite); // Ignore the List<RsiSection> property
                });

                // Configure CodeReference as owned entity
                entity.OwnsOne(e => e.Lintels, l =>
                {
                    l.Property(cr => cr.IdRef).HasMaxLength(50);
                    l.Property(cr => cr.Code).HasMaxLength(50);
                    l.Property(cr => cr.RValue).HasPrecision(18, 2);
                    l.Property(cr => cr.NominalInsulation).HasPrecision(18, 2);
                    l.Property(cr => cr.Text).HasMaxLength(200);
                });
            });

            modelBuilder.Entity<FoundationWallMeasurements>(entity =>
            {
                entity.ToTable("FoundationWallMeasurements", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Depth).HasPrecision(18, 2);
                entity.Property(e => e.PonyWallHeight).HasPrecision(18, 2);
            });

            modelBuilder.Entity<FoundationMeasurements>(entity =>
            {
                entity.ToTable("FoundationMeasurements", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.IsRectangular);
                entity.Property(e => e.Area).HasPrecision(18, 2);
                entity.Property(e => e.Width).HasPrecision(18, 2);
                entity.Property(e => e.Length).HasPrecision(18, 2);
                entity.Property(e => e.Perimeter).HasPrecision(18, 2);
            });

            modelBuilder.Entity<CrawlspaceWallConstruction>(entity =>
            {
                entity.ToTable("CrawlspaceWallConstructions", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Corners);

                // Configure CodeDescriptionAndComposite as owned entity
                entity.OwnsOne(e => e.Type, t =>
                {
                    t.Property(cd => cd.Code).HasMaxLength(50);
                    t.Property(cd => cd.Description).HasMaxLength(200);
                    t.Property(cd => cd.IdRef).HasMaxLength(50);
                    t.Property(cd => cd.NominalInsulation).HasPrecision(18, 2);
                    t.Property(cd => cd.CompositeJson).HasMaxLength(2000);
                    t.Ignore(cd => cd.Composite); // Ignore the List<RsiSection> property
                });

                // Configure CodeReference as owned entity
                entity.OwnsOne(e => e.Lintels, l =>
                {
                    l.Property(cr => cr.IdRef).HasMaxLength(50);
                    l.Property(cr => cr.Code).HasMaxLength(50);
                    l.Property(cr => cr.RValue).HasPrecision(18, 2);
                    l.Property(cr => cr.NominalInsulation).HasPrecision(18, 2);
                    l.Property(cr => cr.Text).HasMaxLength(200);
                });
            });

            modelBuilder.Entity<CrawlspaceWallMeasurements>(entity =>
            {
                entity.ToTable("CrawlspaceWallMeasurements", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Depth).HasPrecision(18, 2);
            });

            modelBuilder.Entity<WallRValues>(entity =>
            {
                entity.ToTable("WallRValues", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Skirt).HasPrecision(18, 2);
                entity.Property(e => e.ThermalBreak).HasPrecision(18, 2);
            });

            modelBuilder.Entity<Configuration>(entity =>
            {
                entity.ToTable("Configurations", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasMaxLength(50);
                entity.Property(e => e.Subtype);
                entity.Property(e => e.Overlap).HasPrecision(18, 2);
            });

            // ===============================
            // WALKOUT-SPECIFIC CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<ExteriorSurfaces>(entity =>
            {
                entity.ToTable("ExteriorSurfaces", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AboveGradeArea).HasPrecision(18, 2);
                entity.Property(e => e.BelowGradeArea).HasPrecision(18, 2);
                entity.Property(e => e.PonyWallArea).HasPrecision(18, 2);
                entity.Property(e => e.SlabPerimeter).HasPrecision(18, 2);
            });

            modelBuilder.Entity<ExposedSurfaces>(entity =>
            {
                entity.ToTable("ExposedSurfaces", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ExteriorAboveGroundArea).HasPrecision(18, 2);
                entity.Property(e => e.ExteriorBelowGroundArea).HasPrecision(18, 2);
                entity.Property(e => e.InteriorAboveGroundArea).HasPrecision(18, 2);
                entity.Property(e => e.InteriorBelowGroundArea).HasPrecision(18, 2);
                entity.Property(e => e.PonyWallArea).HasPrecision(18, 2);
                entity.Property(e => e.WalkoutPerimeter).HasPrecision(18, 2);
                entity.Property(e => e.ExposedPerimeter).HasPrecision(18, 2);
            });

            modelBuilder.Entity<WalkoutMeasurements>(entity =>
            {
                entity.ToTable("WalkoutMeasurements", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.WithSlab);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.D1).HasPrecision(18, 2);
                entity.Property(e => e.D2).HasPrecision(18, 2);
                entity.Property(e => e.D3).HasPrecision(18, 2);
                entity.Property(e => e.D4).HasPrecision(18, 2);
                entity.Property(e => e.D5).HasPrecision(18, 2);
                entity.Property(e => e.L1).HasPrecision(18, 2);
                entity.Property(e => e.L2).HasPrecision(18, 2);
                entity.Property(e => e.L3).HasPrecision(18, 2);
                entity.Property(e => e.L4).HasPrecision(18, 2);
            });

            modelBuilder.Entity<Locations>(entity =>
            {
                entity.ToTable("Locations", "foundation");
                entity.HasKey(e => e.Id);

                // One-to-one relationships with location coordinates - using NoAction to avoid multiple cascade paths
                entity.HasOne(e => e.L1_1)
                      .WithOne()
                      .HasForeignKey<Locations>(l => l.L1_1Id)
                      .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.L1_2)
                      .WithOne()
                      .HasForeignKey<Locations>(l => l.L1_2Id)
                      .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.L2_1)
                      .WithOne()
                      .HasForeignKey<Locations>(l => l.L2_1Id)
                      .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.L2_2)
                      .WithOne()
                      .HasForeignKey<Locations>(l => l.L2_2Id)
                      .OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.Entity<Location>(entity =>
            {
                entity.ToTable("LocationCoordinates", "foundation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.X1).HasPrecision(18, 2);
                entity.Property(e => e.X2).HasPrecision(18, 2);
            });

            // ===============================
            // PERFORMANCE INDEXES
            // ===============================

            modelBuilder.Entity<Foundation>()
                .HasIndex(e => e.HouseId)
                .HasDatabaseName("IX_Foundation_HouseId");
        }
    }
}