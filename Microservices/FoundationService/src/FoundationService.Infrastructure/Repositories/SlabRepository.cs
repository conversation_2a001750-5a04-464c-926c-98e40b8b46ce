using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FoundationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for slab operations following EnvelopeService pattern
    /// </summary>
    public class SlabRepository : ISlabRepository
    {
        private readonly FoundationDbContext _context;
        private readonly ILogger<SlabRepository> _logger;

        public SlabRepository(FoundationDbContext context, ILogger<SlabRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Slab>> GetSlabsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting slabs for house ID: {HouseId}", houseId);
            
            return await _context.Slabs
                .Include(s => s.Configuration)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(s => s.Wall)
                    .ThenInclude(w => w.RValues)
                .Where(s => s.HouseId == houseId)
                .ToListAsync();
        }

        public async Task<Slab?> GetSlabByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting slab by ID: {SlabId}", id);
            
            return await _context.Slabs
                .Include(s => s.Configuration)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(s => s.Wall)
                    .ThenInclude(w => w.RValues)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<Slab>> GetAllSlabsAsync()
        {
            _logger.LogInformation("Getting all slabs");
            
            return await _context.Slabs
                .Include(s => s.Configuration)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(s => s.Wall)
                    .ThenInclude(w => w.RValues)
                .ToListAsync();
        }

        public async Task<Slab> AddSlabAsync(Slab slab)
        {
            _logger.LogInformation("Adding slab for house ID: {HouseId}", slab.HouseId);
            
            _context.Slabs.Add(slab);
            await _context.SaveChangesAsync();
            
            return slab;
        }

        public async Task UpdateSlabAsync(Slab slab)
        {
            _logger.LogInformation("Updating slab with ID: {SlabId}", slab.Id);
            
            var existingSlab = await _context.Slabs
                .Include(s => s.Configuration)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(s => s.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(s => s.Wall)
                    .ThenInclude(w => w.RValues)
                .FirstOrDefaultAsync(s => s.Id == slab.Id);

            if (existingSlab == null)
            {
                throw new InvalidOperationException($"Slab with ID {slab.Id} not found");
            }

            // Update properties manually following EnvelopeService pattern
            existingSlab.HouseId = slab.HouseId;
            existingSlab.Label = slab.Label;
            existingSlab.IsExposedSurface = slab.IsExposedSurface;
            existingSlab.ExposedSurfacePerimeter = slab.ExposedSurfacePerimeter;

            // Update configuration
            if (slab.Configuration != null && existingSlab.Configuration != null)
            {
                existingSlab.Configuration.Type = slab.Configuration.Type;
                existingSlab.Configuration.Subtype = slab.Configuration.Subtype;
                existingSlab.Configuration.Overlap = slab.Configuration.Overlap;
            }

            // Update floor
            if (slab.Floor != null && existingSlab.Floor != null)
            {
                if (slab.Floor.Construction != null && existingSlab.Floor.Construction != null)
                {
                    existingSlab.Floor.Construction.IsBelowFrostline = slab.Floor.Construction.IsBelowFrostline;
                    existingSlab.Floor.Construction.HasIntegralFooting = slab.Floor.Construction.HasIntegralFooting;
                    existingSlab.Floor.Construction.HeatedFloor = slab.Floor.Construction.HeatedFloor;
                    existingSlab.Floor.Construction.AddedToSlab = slab.Floor.Construction.AddedToSlab;
                    existingSlab.Floor.Construction.FloorsAbove = slab.Floor.Construction.FloorsAbove;
                }

                if (slab.Floor.Measurements != null && existingSlab.Floor.Measurements != null)
                {
                    existingSlab.Floor.Measurements.IsRectangular = slab.Floor.Measurements.IsRectangular;
                    existingSlab.Floor.Measurements.Area = slab.Floor.Measurements.Area;
                    existingSlab.Floor.Measurements.Width = slab.Floor.Measurements.Width;
                    existingSlab.Floor.Measurements.Length = slab.Floor.Measurements.Length;
                    existingSlab.Floor.Measurements.Perimeter = slab.Floor.Measurements.Perimeter;
                }
            }

            // Update wall
            if (slab.Wall != null && existingSlab.Wall != null)
            {
                if (slab.Wall.RValues != null && existingSlab.Wall.RValues != null)
                {
                    existingSlab.Wall.RValues.Skirt = slab.Wall.RValues.Skirt;
                    existingSlab.Wall.RValues.ThermalBreak = slab.Wall.RValues.ThermalBreak;
                }
            }

            _context.Entry(existingSlab).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteSlabAsync(Guid id)
        {
            _logger.LogInformation("Deleting slab with ID: {SlabId}", id);
            
            var slab = await _context.Slabs.FindAsync(id);
            if (slab != null)
            {
                _context.Slabs.Remove(slab);
                await _context.SaveChangesAsync();
            }
        }
    }
}
