using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FoundationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for basement operations following EnvelopeService pattern
    /// </summary>
    public class BasementRepository : IBasementRepository
    {
        private readonly FoundationDbContext _context;
        private readonly ILogger<BasementRepository> _logger;

        public BasementRepository(FoundationDbContext context, ILogger<BasementRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Basement>> GetBasementsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting basements for house ID: {HouseId}", houseId);
            
            return await _context.Basements
                .Include(b => b.Configuration)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Measurements)
                .Where(b => b.HouseId == houseId)
                .ToListAsync();
        }

        public async Task<Basement?> GetBasementByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting basement by ID: {BasementId}", id);
            
            return await _context.Basements
                .Include(b => b.Configuration)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Measurements)
                .FirstOrDefaultAsync(b => b.Id == id);
        }

        public async Task<IEnumerable<Basement>> GetAllBasementsAsync()
        {
            _logger.LogInformation("Getting all basements");
            
            return await _context.Basements
                .Include(b => b.Configuration)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Measurements)
                .ToListAsync();
        }

        public async Task<Basement> AddBasementAsync(Basement basement)
        {
            _logger.LogInformation("Adding basement for house ID: {HouseId}", basement.HouseId);
            
            _context.Basements.Add(basement);
            await _context.SaveChangesAsync();
            
            return basement;
        }

        public async Task UpdateBasementAsync(Basement basement)
        {
            _logger.LogInformation("Updating basement with ID: {BasementId}", basement.Id);
            
            var existingBasement = await _context.Basements
                .Include(b => b.Configuration)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(b => b.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(b => b.Wall)
                    .ThenInclude(w => w.Measurements)
                .FirstOrDefaultAsync(b => b.Id == basement.Id);

            if (existingBasement == null)
            {
                throw new InvalidOperationException($"Basement with ID {basement.Id} not found");
            }

            // Update properties manually following EnvelopeService pattern
            existingBasement.HouseId = basement.HouseId;
            existingBasement.Label = basement.Label;
            existingBasement.IsExposedSurface = basement.IsExposedSurface;
            existingBasement.ExposedSurfacePerimeter = basement.ExposedSurfacePerimeter;

            // Update owned entities
            if (basement.OpeningUpstairs != null)
            {
                existingBasement.OpeningUpstairs = basement.OpeningUpstairs;
            }

            if (basement.RoomType != null)
            {
                existingBasement.RoomType = basement.RoomType;
            }

            // Update configuration
            if (basement.Configuration != null && existingBasement.Configuration != null)
            {
                existingBasement.Configuration.Type = basement.Configuration.Type;
                existingBasement.Configuration.Subtype = basement.Configuration.Subtype;
                existingBasement.Configuration.Overlap = basement.Configuration.Overlap;
            }

            // Update floor
            if (basement.Floor != null && existingBasement.Floor != null)
            {
                if (basement.Floor.Construction != null && existingBasement.Floor.Construction != null)
                {
                    existingBasement.Floor.Construction.IsBelowFrostline = basement.Floor.Construction.IsBelowFrostline;
                    existingBasement.Floor.Construction.HasIntegralFooting = basement.Floor.Construction.HasIntegralFooting;
                    existingBasement.Floor.Construction.HeatedFloor = basement.Floor.Construction.HeatedFloor;
                    existingBasement.Floor.Construction.AddedToSlab = basement.Floor.Construction.AddedToSlab;
                    existingBasement.Floor.Construction.FloorsAbove = basement.Floor.Construction.FloorsAbove;
                }

                if (basement.Floor.Measurements != null && existingBasement.Floor.Measurements != null)
                {
                    existingBasement.Floor.Measurements.IsRectangular = basement.Floor.Measurements.IsRectangular;
                    existingBasement.Floor.Measurements.Area = basement.Floor.Measurements.Area;
                    existingBasement.Floor.Measurements.Width = basement.Floor.Measurements.Width;
                    existingBasement.Floor.Measurements.Length = basement.Floor.Measurements.Length;
                    existingBasement.Floor.Measurements.Perimeter = basement.Floor.Measurements.Perimeter;
                }
            }

            // Update wall
            if (basement.Wall != null && existingBasement.Wall != null)
            {
                existingBasement.Wall.HasPonyWall = basement.Wall.HasPonyWall;

                if (basement.Wall.Construction != null && existingBasement.Wall.Construction != null)
                {
                    existingBasement.Wall.Construction.Corners = basement.Wall.Construction.Corners;
                    existingBasement.Wall.Construction.InteriorAddedInsulation = basement.Wall.Construction.InteriorAddedInsulation;
                    existingBasement.Wall.Construction.ExteriorAddedInsulation = basement.Wall.Construction.ExteriorAddedInsulation;
                    existingBasement.Wall.Construction.Lintels = basement.Wall.Construction.Lintels;
                    existingBasement.Wall.Construction.PonyWallType = basement.Wall.Construction.PonyWallType;
                }

                if (basement.Wall.Measurements != null && existingBasement.Wall.Measurements != null)
                {
                    existingBasement.Wall.Measurements.Height = basement.Wall.Measurements.Height;
                    existingBasement.Wall.Measurements.Depth = basement.Wall.Measurements.Depth;
                    existingBasement.Wall.Measurements.PonyWallHeight = basement.Wall.Measurements.PonyWallHeight;
                }
            }

            _context.Entry(existingBasement).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteBasementAsync(Guid id)
        {
            _logger.LogInformation("Deleting basement with ID: {BasementId}", id);
            
            var basement = await _context.Basements.FindAsync(id);
            if (basement != null)
            {
                _context.Basements.Remove(basement);
                await _context.SaveChangesAsync();
            }
        }
    }
}
