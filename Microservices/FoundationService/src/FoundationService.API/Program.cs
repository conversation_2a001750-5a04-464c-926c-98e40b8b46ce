using System;
using System.Data;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Services;
using FoundationService.Infrastructure.Data;
using FoundationService.Infrastructure.Repositories;


var builder = WebApplication.CreateBuilder(args);

// Add services to the container (following AirInfiltrationService pattern)
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

// Configure Swagger/OpenAPI (following AirInfiltrationService pattern)
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Foundation Service API",
        Version = "v1",
        Description = "A microservice for managing foundation systems in the HOT2000 system",
        Contact = new OpenApiContact
        {
            Name = "HOT2000 Team"
        }
    });
});

// Configure DbContext with schema support (following AirInfiltrationService pattern)
builder.Services.AddDbContext<FoundationDbContext>(options =>
{
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlOptions =>
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 5,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(300);
            // Set the migrations history table schema (following AirInfiltrationService pattern)
            sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "foundation");
        });
});

// Configure AutoMapper (following AirInfiltrationService pattern)
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Configure Repositories and Services (following EnvelopeService pattern)
builder.Services.AddScoped<IBasementRepository, BasementRepository>();
builder.Services.AddScoped<ICrawlspaceRepository, CrawlspaceRepository>();
builder.Services.AddScoped<ISlabRepository, SlabRepository>();
builder.Services.AddScoped<IWalkoutRepository, WalkoutRepository>();

builder.Services.AddScoped<IBasementService, BasementService>();
builder.Services.AddScoped<ICrawlspaceService, CrawlspaceService>();
builder.Services.AddScoped<ISlabService, SlabService>();
builder.Services.AddScoped<IWalkoutService, WalkoutService>();



// Configure CORS (following AirInfiltrationService pattern)
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

var app = builder.Build();

// Enable Swagger in all environments (following AirInfiltrationService pattern)
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Foundation Service API V1");
    c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
});

// Database initialization with robust error handling (following AirInfiltrationService pattern)
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Starting database initialization...");
        var dbContext = services.GetRequiredService<FoundationDbContext>();

        // Test database connection first
        logger.LogInformation("Testing database connection...");
        var canConnect = await dbContext.Database.CanConnectAsync();

        if (!canConnect)
        {
            logger.LogError("Cannot connect to database");
            throw new InvalidOperationException("Database connection failed");
        }

        logger.LogInformation("Database connection successful");

        var connection = dbContext.Database.GetDbConnection();
        if (connection.State != ConnectionState.Open)
            await connection.OpenAsync();

        // Check for existing tables
        bool hasExistingTables = false;
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = 'foundation'";

            var tableCount = Convert.ToInt32(await command.ExecuteScalarAsync());
            hasExistingTables = tableCount > 0;

            logger.LogInformation($"Found {tableCount} existing tables in foundation schema");
        }

        if (hasExistingTables)
        {
            logger.LogInformation("Dropping existing foundation schema for clean start...");

            try
            {
                // More robust schema dropping (following AirInfiltrationService pattern)
                using (var command = connection.CreateCommand())
                {
                    // Drop foreign key constraints first
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'ALTER TABLE [foundation].[' + TABLE_NAME + '] DROP CONSTRAINT [' + CONSTRAINT_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
                        WHERE CONSTRAINT_SCHEMA = 'foundation' AND CONSTRAINT_TYPE = 'FOREIGN KEY'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();

                    // Drop tables
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'DROP TABLE [foundation].[' + TABLE_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_SCHEMA = 'foundation'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();

                    // Drop schema
                    command.CommandText = @"
                        IF EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'foundation')
                        BEGIN
                            DROP SCHEMA [foundation]
                        END";
                    await command.ExecuteNonQueryAsync();
                }

                logger.LogInformation("Foundation schema dropped successfully");
            }
            catch (Exception dropEx)
            {
                logger.LogWarning($"Error during schema drop: {dropEx.Message}");
                logger.LogInformation("Continuing with migration...");
            }
        }

        // Create schema (following AirInfiltrationService pattern)
        using (var command = connection.CreateCommand())
        {
            command.CommandText = "IF NOT EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'foundation') BEGIN EXEC('CREATE SCHEMA foundation') END";
            await command.ExecuteNonQueryAsync();
        }

        // Apply migrations (following EnvelopeService pattern)
        logger.LogInformation("Applying migrations...");
        await dbContext.Database.MigrateAsync();

        logger.LogInformation("Database initialization completed successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Database initialization failed");

        // Don't throw in startup - let the service run without DB for debugging (following AirInfiltrationService pattern)
        logger.LogWarning("Service will start without database initialization");
    }
}

// Configure middleware pipeline (following AirInfiltrationService pattern)
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseCors("AllowAll");
app.UseRouting();
app.UseAuthorization();
app.MapControllers();

app.Run();