using AutoMapper;
using FoundationService.Core.Models;
using FoundationService.API.Models;

namespace FoundationService.API.Models
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // ===============================
            // FOUNDATION BASE CLASS MAPPINGS (Following EnvelopeService pattern)
            // ===============================

            CreateMap<Foundation, FoundationDto>()
                .IncludeAllDerived()
                .ReverseMap();

            // ===============================
            // FOUNDATION TYPE MAPPINGS (Following EnvelopeService pattern)
            // ===============================

            CreateMap<Basement, BasementDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Floor != null)
                    {
                        dest.Floor.Id = dest.FloorId;
                    }
                    if (dest.Wall != null)
                    {
                        dest.Wall.Id = dest.WallId;
                    }
                });

            CreateMap<Crawlspace, CrawlspaceDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Floor != null)
                    {
                        dest.Floor.Id = dest.FloorId;
                    }
                    if (dest.Wall != null)
                    {
                        dest.Wall.Id = dest.WallId;
                    }
                });

            CreateMap<Slab, SlabDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Floor != null)
                    {
                        dest.Floor.Id = dest.FloorId;
                    }
                    if (dest.Wall != null)
                    {
                        dest.Wall.Id = dest.WallId;
                    }
                });

            CreateMap<Walkout, WalkoutDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Measurements != null)
                    {
                        dest.Measurements.Id = dest.MeasurementsId;
                    }
                    if (dest.Floor != null)
                    {
                        dest.Floor.Id = dest.FloorId;
                    }
                    if (dest.Wall != null)
                    {
                        dest.Wall.Id = dest.WallId;
                    }
                    if (dest.ExteriorSurfaces != null)
                    {
                        dest.ExteriorSurfaces.Id = dest.ExteriorSurfacesId;
                    }
                    if (dest.Locations != null)
                    {
                        dest.Locations.Id = dest.LocationsId;
                    }
                });

            // ===============================
            // COMPONENT MAPPINGS (Following EnvelopeService pattern)
            // ===============================

            CreateMap<FoundationFloor, FoundationFloorDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.Id = dest.ConstructionId;
                    }
                    if (dest.Measurements != null)
                    {
                        dest.Measurements.Id = dest.MeasurementsId;
                    }
                });

            CreateMap<FoundationWall, FoundationWallDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.Id = dest.ConstructionId;
                    }
                    if (dest.Measurements != null)
                    {
                        dest.Measurements.Id = dest.MeasurementsId;
                    }
                });

            CreateMap<CrawlspaceWall, CrawlspaceWallDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.Id = dest.ConstructionId ?? Guid.NewGuid();
                    }
                    if (dest.Measurements != null)
                    {
                        dest.Measurements.Id = dest.MeasurementsId;
                    }
                    if (dest.RValues != null)
                    {
                        dest.RValues.Id = dest.RValuesId;
                    }
                });

            CreateMap<SlabWall, SlabWallDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.RValues != null)
                    {
                        dest.RValues.Id = dest.RValuesId;
                    }
                });

            CreateMap<WalkoutFloor, WalkoutFloorDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Construction != null)
                    {
                        dest.Construction.Id = dest.ConstructionId;
                    }
                });

            // ===============================
            // CONSTRUCTION AND MEASUREMENT MAPPINGS (Following EnvelopeService pattern)
            // ===============================

            CreateMap<FoundationFloorConstruction, FoundationFloorConstructionDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.AddedToSlab == null)
                    {
                        dest.AddedToSlab = new CodeReference();
                    }
                    if (dest.FloorsAbove == null)
                    {
                        dest.FloorsAbove = new CodeReference();
                    }
                });

            CreateMap<FoundationWallConstruction, FoundationWallConstructionDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.InteriorAddedInsulation == null)
                    {
                        dest.InteriorAddedInsulation = new CodeDescriptionAndComposite();
                    }
                    if (dest.ExteriorAddedInsulation == null)
                    {
                        dest.ExteriorAddedInsulation = new CodeDescriptionAndComposite();
                    }
                    if (dest.Lintels == null)
                    {
                        dest.Lintels = new CodeReference();
                    }
                    if (dest.PonyWallType == null)
                    {
                        dest.PonyWallType = new CodeDescriptionAndComposite();
                    }
                });

            CreateMap<FoundationWallMeasurements, FoundationWallMeasurementsDto>()
                .ReverseMap();

            CreateMap<FoundationMeasurements, FoundationMeasurementsDto>()
                .ReverseMap();

            CreateMap<CrawlspaceWallConstruction, CrawlspaceWallConstructionDto>().ReverseMap()
                .AfterMap((src, dest) =>
                {
                    if (dest.Type == null)
                    {
                        dest.Type = new CodeDescriptionAndComposite();
                    }
                    if (dest.Lintels == null)
                    {
                        dest.Lintels = new CodeReference();
                    }
                });

            CreateMap<CrawlspaceWallMeasurements, CrawlspaceWallMeasurementsDto>()
                .ReverseMap();

            CreateMap<WallRValues, WallRValuesDto>()
                .ReverseMap();

            // ===============================
            // WALKOUT-SPECIFIC MAPPINGS
            // ===============================

            CreateMap<WalkoutMeasurements, WalkoutMeasurementsDto>()
                .ReverseMap();

            CreateMap<ExteriorSurfaces, ExteriorSurfacesDto>()
                .ReverseMap();

            CreateMap<ExposedSurfaces, ExposedSurfacesDto>()
                .ReverseMap();

            CreateMap<Locations, LocationsDto>()
                .ReverseMap();

            CreateMap<Location, LocationDto>()
                .ReverseMap();

            // ===============================
            // CONFIGURATION AND SUPPORT MAPPINGS
            // ===============================

            CreateMap<Configuration, ConfigurationDto>()
                .ReverseMap();

            // ===============================
            // RESOURCE MAPPINGS (Following EnvelopeService pattern)
            // ===============================

            // Resource mappings with automatic text population from static resource definitions
            CreateMap<OpeningsUpstairs, OpeningsUpstairsDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<OpeningsUpstairsDto, OpeningsUpstairs>()
                .ConstructUsing(src =>
                    OpeningsUpstairs.All.FirstOrDefault(ou => ou.Code == (src.Code ?? "1")) ??
                    OpeningsUpstairs.StandardDoorClosed);

            CreateMap<RoomTypes, RoomTypesDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<RoomTypesDto, RoomTypes>()
                .ConstructUsing(src =>
                    RoomTypes.All.FirstOrDefault(rt => rt.Code == (src.Code ?? "1")) ??
                    RoomTypes.Other);

            CreateMap<VentilationTypes, VentilationTypesDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<VentilationTypesDto, VentilationTypes>()
                .ConstructUsing(src =>
                    VentilationTypes.All.FirstOrDefault(vt => vt.Code == (src.Code ?? "1")) ??
                    VentilationTypes.Closed);

            // ===============================
            // CODE AND TEXT MAPPINGS
            // ===============================

            CreateMap<CodeAndText, CodeAndTextDto>()
                .ReverseMap();

            CreateMap<CodeTextAndValue, CodeTextAndValueDto>()
                .ReverseMap();

            CreateMap<CodeReference, CodeReferenceDto>()
                .ReverseMap();

            CreateMap<CodeDescriptionAndComposite, CodeDescriptionAndCompositeDto>()
                .ReverseMap();

            CreateMap<RsiSection, RsiSectionDto>()
                .ReverseMap();

            // ===============================
            // RESOURCE LIST MAPPINGS
            // ===============================

            CreateMap<ResourceList, ResourceListDto>()
                .ReverseMap();

            CreateMap<ResourceValueList, ResourceValueListDto>()
                .ReverseMap();
        }
    }
}