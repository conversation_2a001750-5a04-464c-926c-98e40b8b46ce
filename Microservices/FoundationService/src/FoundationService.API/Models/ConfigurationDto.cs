using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for foundation configuration
    /// </summary>
    public class ConfigurationDto
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public uint Subtype { get; set; } = 0;
        public decimal Overlap { get; set; } = 0.0m;
        public string Text => string.Format("{0}_{1}", Type, Subtype);
    }
}
