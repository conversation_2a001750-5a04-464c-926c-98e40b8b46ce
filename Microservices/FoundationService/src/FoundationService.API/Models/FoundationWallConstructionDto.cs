using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for foundation wall construction details
    /// </summary>
    public class FoundationWallConstructionDto
    {
        public Guid Id { get; set; }
        public ushort Corners { get; set; } = 0;
        public CodeDescriptionAndCompositeDto InteriorAddedInsulation { get; set; } = new CodeDescriptionAndCompositeDto();
        public CodeDescriptionAndCompositeDto ExteriorAddedInsulation { get; set; } = new CodeDescriptionAndCompositeDto();
        public CodeReferenceDto Lintels { get; set; } = new CodeReferenceDto();
        public CodeDescriptionAndCompositeDto PonyWallType { get; set; } = new CodeDescriptionAndCompositeDto();
    }
}
