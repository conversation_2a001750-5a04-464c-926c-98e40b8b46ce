using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for foundation floor construction details
    /// </summary>
    public class FoundationFloorConstructionDto
    {
        public Guid Id { get; set; }
        public bool IsBelowFrostline { get; set; } = false;
        public bool HasIntegralFooting { get; set; } = false;
        public bool HeatedFloor { get; set; } = false;
        public CodeReferenceDto AddedToSlab { get; set; } = new CodeReferenceDto();
        public CodeReferenceDto FloorsAbove { get; set; } = new CodeReferenceDto();
    }
}
