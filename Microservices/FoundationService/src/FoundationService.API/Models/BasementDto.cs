using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for basement foundation type
    /// </summary>
    public class BasementDto : FoundationDto
    {
        public OpeningsUpstairsDto OpeningUpstairs { get; set; } = new OpeningsUpstairsDto();
        public RoomTypesDto RoomType { get; set; } = new RoomTypesDto();
        public FoundationFloorDto Floor { get; set; } = new FoundationFloorDto();
        public FoundationWallDto Wall { get; set; } = new FoundationWallDto();
    }
}
