using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for crawlspace wall details
    /// </summary>
    public class CrawlspaceWallDto
    {
        public Guid Id { get; set; }
        public CrawlspaceWallConstructionDto? Construction { get; set; } = new CrawlspaceWallConstructionDto();
        public CrawlspaceWallMeasurementsDto Measurements { get; set; } = new CrawlspaceWallMeasurementsDto();
        public WallRValuesDto RValues { get; set; } = new WallRValuesDto();
    }
}
