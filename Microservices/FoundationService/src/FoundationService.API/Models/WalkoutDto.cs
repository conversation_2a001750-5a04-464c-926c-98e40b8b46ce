using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for walkout foundation type
    /// </summary>
    public class WalkoutDto : FoundationDto
    {
        public OpeningsUpstairsDto OpeningUpstairs { get; set; } = new OpeningsUpstairsDto();
        public RoomTypesDto RoomType { get; set; } = new RoomTypesDto();
        public WalkoutMeasurementsDto Measurements { get; set; } = new WalkoutMeasurementsDto();
        public WalkoutFloorDto Floor { get; set; } = new WalkoutFloorDto();
        public FoundationWallDto Wall { get; set; } = new FoundationWallDto();
        public ExteriorSurfacesDto ExteriorSurfaces { get; set; } = new ExteriorSurfacesDto();
        public LocationsDto Locations { get; set; } = new LocationsDto();
    }
}
