using System;
using System.Collections.Generic;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for code description and composite insulation
    /// </summary>
    public class CodeDescriptionAndCompositeDto
    {
        public string Description { get; set; } = string.Empty;
        public List<RsiSectionDto> Composite { get; set; } = new List<RsiSectionDto>();
        public string IdRef { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public decimal NominalInsulation { get; set; } = 0.0m;
    }

    /// <summary>
    /// DTO for RSI section in composite insulation
    /// </summary>
    public class RsiSectionDto
    {
        public decimal Percentage { get; set; } = 0.0m;
        public decimal Rsi { get; set; } = 0.0m;
    }
}
