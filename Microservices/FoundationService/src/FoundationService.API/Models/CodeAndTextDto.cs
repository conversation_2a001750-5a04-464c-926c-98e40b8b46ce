namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for code and text information following AirInfiltrationService pattern
    /// </summary>
    public class CodeAndTextDto
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for code, text and value information following AirInfiltrationService pattern
    /// </summary>
    public class CodeTextAndValueDto : CodeAndTextDto
    {
        public decimal Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }
}
