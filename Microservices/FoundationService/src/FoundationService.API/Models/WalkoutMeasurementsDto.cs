using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for walkout foundation measurements
    /// </summary>
    public class WalkoutMeasurementsDto
    {
        public Guid Id { get; set; }
        public bool WithSlab { get; set; } = false;
        public decimal Height { get; set; } = 0.0m;
        public decimal D1 { get; set; } = 0.0m;
        public decimal D2 { get; set; } = 0.0m;
        public decimal D3 { get; set; } = 0.0m;
        public decimal D4 { get; set; } = 0.0m;
        public decimal D5 { get; set; } = 0.0m;
        public decimal L1 { get; set; } = 0.0m;
        public decimal L2 { get; set; } = 0.0m;
        public decimal L3 { get; set; } = 0.0m;
        public decimal L4 { get; set; } = 0.0m;
    }
}
