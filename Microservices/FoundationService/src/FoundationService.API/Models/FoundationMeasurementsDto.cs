using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for foundation measurements
    /// </summary>
    public class FoundationMeasurementsDto
    {
        public Guid Id { get; set; }
        public bool IsRectangular { get; set; } = false;
        public decimal Area { get; set; } = 0.0m;
        public decimal Width { get; set; } = 0.0m;
        public decimal Length { get; set; } = 0.0m;
        public decimal Perimeter { get; set; } = 0.0m;
        public decimal FloorArea => IsRectangular ? Width * Length : Area;
    }
}
