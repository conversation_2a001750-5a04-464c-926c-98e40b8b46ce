using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for exposed surfaces for foundation calculations
    /// </summary>
    public class ExposedSurfacesDto
    {
        public Guid Id { get; set; }
        public decimal ExteriorAboveGroundArea { get; set; } = 0.0m;
        public decimal ExteriorBelowGroundArea { get; set; } = 0.0m;
        public decimal InteriorAboveGroundArea { get; set; } = 0.0m;
        public decimal InteriorBelowGroundArea { get; set; } = 0.0m;
        public decimal PonyWallArea { get; set; } = 0.0m;
        public decimal WalkoutPerimeter { get; set; } = 0.0m;
        public decimal ExposedPerimeter { get; set; } = 0.0m;
    }
}
