namespace FoundationService.API.Models
{
    /// <summary>
    /// Base DTO for resource list items following AirInfiltrationService approach
    /// </summary>
    public class ResourceListDto
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; } = false;
    }

    /// <summary>
    /// Resource list DTO with numeric value support
    /// </summary>
    public class ResourceValueListDto : ResourceListDto
    {
        public decimal Value { get; set; } = 0.0m;
    }
}
