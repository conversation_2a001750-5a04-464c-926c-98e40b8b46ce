using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// DTO for crawlspace foundation type
    /// </summary>
    public class CrawlspaceDto : FoundationDto
    {
        public VentilationTypesDto VentilationType { get; set; } = new VentilationTypesDto();
        public FoundationFloorDto Floor { get; set; } = new FoundationFloorDto();
        public CrawlspaceWallDto Wall { get; set; } = new CrawlspaceWallDto();
    }
}