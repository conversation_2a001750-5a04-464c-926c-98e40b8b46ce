# Use standard Microsoft ASP.NET base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
# Install curl for health checks (following AirInfiltrationService pattern)
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy Foundation service projects (following AirInfiltrationService pattern)
COPY ["FoundationService/src/FoundationService.API/FoundationService.API.csproj", "FoundationService/src/FoundationService.API/"]
COPY ["FoundationService/src/FoundationService.Core/FoundationService.Core.csproj", "FoundationService/src/FoundationService.Core/"]
COPY ["FoundationService/src/FoundationService.Infrastructure/FoundationService.Infrastructure.csproj", "FoundationService/src/FoundationService.Infrastructure/"]

# Copy the shared validation project (needed for project reference)
COPY ["Hot2K.Shared.Validation/Hot2K.Shared.Validation.csproj", "Hot2K.Shared.Validation/"]

# Restore dependencies (following AirInfiltrationService pattern)
RUN dotnet restore "FoundationService/src/FoundationService.API/FoundationService.API.csproj"

# Copy the shared validation library source code (following AirInfiltrationService pattern)
COPY ["Hot2K.Shared.Validation/", "Hot2K.Shared.Validation/"]

# Copy the shared validation rules (following AirInfiltrationService pattern)
COPY ["ValidationRules/", "ValidationRules/"]

# Add required packages for migrations (following AirInfiltrationService pattern)
RUN dotnet add "FoundationService/src/FoundationService.Infrastructure/FoundationService.Infrastructure.csproj" package Microsoft.EntityFrameworkCore.Design --version 8.0.15
RUN dotnet add "FoundationService/src/FoundationService.Infrastructure/FoundationService.Infrastructure.csproj" package Microsoft.EntityFrameworkCore.Tools --version 8.0.15

# Install EF Core tools (following AirInfiltrationService pattern)
RUN dotnet tool install --global dotnet-ef
ENV PATH="${PATH}:/root/.dotnet/tools"

# Copy the Foundation service source code (following AirInfiltrationService pattern)
COPY ./FoundationService/ ./FoundationService/

# Create migrations directory if it doesn't exist
RUN mkdir -p FoundationService/src/FoundationService.Infrastructure/Data/Migrations

# Build the solution first to ensure everything compiles (following AirInfiltrationService pattern)
WORKDIR "/src"
RUN dotnet build "FoundationService/src/FoundationService.API/FoundationService.API.csproj" -c Release

# Create FoundationDbContext migration (following AirInfiltrationService pattern)
WORKDIR "/src"
RUN dotnet ef migrations add InitialFoundationCreate \
    --project FoundationService/src/FoundationService.Infrastructure \
    --startup-project FoundationService/src/FoundationService.API \
    --context FoundationDbContext \
    --output-dir Data/Migrations/Foundation \
    || echo "Migration creation skipped - will use EnsureCreated instead"

# Build the application (following AirInfiltrationService pattern)
WORKDIR "/src/FoundationService/src/FoundationService.API"
RUN dotnet build "FoundationService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FoundationService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Copy validation rules to the runtime container (following AirInfiltrationService pattern)
COPY --from=build /src/ValidationRules/ ./ValidationRules/

ENTRYPOINT ["dotnet", "FoundationService.API.dll"]