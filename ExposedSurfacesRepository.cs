// Example: Direct ExposedSurfaces Repository Implementation

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;

namespace FoundationService.Infrastructure.Repositories
{
    public interface IExposedSurfacesRepository
    {
        Task<ExposedSurfaces> GetByIdAsync(Guid id);
        Task<IEnumerable<ExposedSurfaces>> GetAllAsync();
        Task<ExposedSurfaces> AddAsync(ExposedSurfaces exposedSurfaces);
        Task UpdateAsync(ExposedSurfaces exposedSurfaces);
        Task DeleteAsync(Guid id);
    }

    public class ExposedSurfacesRepository : IExposedSurfacesRepository
    {
        private readonly FoundationDbContext _context;
        private readonly ILogger<ExposedSurfacesRepository> _logger;

        public ExposedSurfacesRepository(FoundationDbContext context, ILogger<ExposedSurfacesRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ExposedSurfaces> GetByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting exposed surfaces with ID: {Id}", id);
            
            var exposedSurfaces = await _context.ExposedSurfaces
                .FirstOrDefaultAsync(es => es.Id == id);
                
            if (exposedSurfaces == null)
            {
                _logger.LogWarning("Exposed surfaces with ID {Id} not found", id);
            }
            
            return exposedSurfaces;
        }

        public async Task<IEnumerable<ExposedSurfaces>> GetAllAsync()
        {
            _logger.LogInformation("Getting all exposed surfaces");
            
            return await _context.ExposedSurfaces.ToListAsync();
        }

        public async Task<ExposedSurfaces> AddAsync(ExposedSurfaces exposedSurfaces)
        {
            _logger.LogInformation("Adding new exposed surfaces");
            
            if (exposedSurfaces.Id == Guid.Empty)
            {
                exposedSurfaces.Id = Guid.NewGuid();
            }
            
            _context.ExposedSurfaces.Add(exposedSurfaces);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Added exposed surfaces with ID: {Id}", exposedSurfaces.Id);
            return exposedSurfaces;
        }

        public async Task UpdateAsync(ExposedSurfaces exposedSurfaces)
        {
            _logger.LogInformation("Updating exposed surfaces with ID: {Id}", exposedSurfaces.Id);
            
            var existingExposedSurfaces = await _context.ExposedSurfaces
                .FirstOrDefaultAsync(es => es.Id == exposedSurfaces.Id);
                
            if (existingExposedSurfaces == null)
            {
                throw new ArgumentException($"Exposed surfaces with ID {exposedSurfaces.Id} not found");
            }

            // Update properties
            existingExposedSurfaces.ExteriorAboveGroundArea = exposedSurfaces.ExteriorAboveGroundArea;
            existingExposedSurfaces.ExteriorBelowGroundArea = exposedSurfaces.ExteriorBelowGroundArea;
            existingExposedSurfaces.InteriorAboveGroundArea = exposedSurfaces.InteriorAboveGroundArea;
            existingExposedSurfaces.InteriorBelowGroundArea = exposedSurfaces.InteriorBelowGroundArea;
            existingExposedSurfaces.PonyWallArea = exposedSurfaces.PonyWallArea;
            existingExposedSurfaces.WalkoutPerimeter = exposedSurfaces.WalkoutPerimeter;
            existingExposedSurfaces.ExposedPerimeter = exposedSurfaces.ExposedPerimeter;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated exposed surfaces with ID: {Id}", exposedSurfaces.Id);
        }

        public async Task DeleteAsync(Guid id)
        {
            _logger.LogInformation("Deleting exposed surfaces with ID: {Id}", id);
            
            var exposedSurfaces = await _context.ExposedSurfaces
                .FirstOrDefaultAsync(es => es.Id == id);
                
            if (exposedSurfaces == null)
            {
                throw new ArgumentException($"Exposed surfaces with ID {id} not found");
            }

            _context.ExposedSurfaces.Remove(exposedSurfaces);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Deleted exposed surfaces with ID: {Id}", id);
        }
    }
}

// Usage example:
// var exposedSurfaces = new ExposedSurfaces
// {
//     ExteriorAboveGroundArea = 45.2m,
//     ExteriorBelowGroundArea = 32.1m,
//     InteriorAboveGroundArea = 0m,
//     InteriorBelowGroundArea = 0m,
//     PonyWallArea = 15.3m,
//     WalkoutPerimeter = 0m,
//     ExposedPerimeter = 25.5m
// };
// 
// var saved = await exposedSurfacesRepository.AddAsync(exposedSurfaces);
